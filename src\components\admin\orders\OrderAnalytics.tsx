"use client";

import { useState, useEffect } from "react";
import {
  FiDollarSign,
  FiShoppingBag,
  FiTrendingUp,
  FiTrendingDown,
  FiUsers,
  FiCalendar,
  FiRefreshCw,
  FiBarChart,
} from "react-icons/fi";
import { OrderAnalyticsWithStats } from "@/services/api";
import { ordersApi } from "@/services/ordersApi";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>hnut<PERSON>hart, 
  createLineChartData, 
  createBar<PERSON>hartD<PERSON>, 
  createPieChartData,
  chartColors 
} from "../charts/ChartComponents";

const OrderAnalytics = () => {
  const [analytics, setAnalytics] = useState<{
    lastDay: OrderAnalyticsWithStats | null;
    lastMonth: OrderAnalyticsWithStats | null;
    lastYear: OrderAnalyticsWithStats | null;
    last5Years: OrderAnalyticsWithStats | null;
  }>({
    lastDay: null,
    lastMonth: null,
    lastYear: null,
    last5Years: null,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'lastDay' | 'lastMonth' | 'lastYear' | 'last5Years'>('lastMonth');

  // Fetch analytics data
  const fetchAnalytics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const [lastDay, lastMonth, lastYear, last5Years] = await Promise.all([
        ordersApi.getLastDayAnalytics(),
        ordersApi.getLastMonthAnalytics(),
        ordersApi.getLastYearAnalytics(),
        ordersApi.getLast5YearsAnalytics(),
      ]);

      setAnalytics({
        lastDay,
        lastMonth,
        lastYear,
        last5Years,
      });
    } catch (error) {
      console.error("Error fetching analytics:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch analytics");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format number with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  // Get current analytics data
  const currentAnalytics = analytics[selectedPeriod];

  // Create revenue trend chart data
  const createRevenueTrendData = () => {
    if (!currentAnalytics) return null;

    if (selectedPeriod === 'lastDay' && currentAnalytics.dailyStats) {
      return createLineChartData(
        currentAnalytics.dailyStats.map(stat => new Date(stat.date).toLocaleDateString()),
        [{
          label: 'Revenue',
          data: currentAnalytics.dailyStats.map(stat => stat.totalRevenue),
          color: chartColors.primary,
          fill: true,
        }]
      );
    }

    if (selectedPeriod === 'lastMonth' && currentAnalytics.dailyStats) {
      return createLineChartData(
        currentAnalytics.dailyStats.map(stat => new Date(stat.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
        [{
          label: 'Revenue',
          data: currentAnalytics.dailyStats.map(stat => stat.totalRevenue),
          color: chartColors.primary,
          fill: true,
        }]
      );
    }

    if (selectedPeriod === 'lastYear' && currentAnalytics.monthlyStats) {
      return createLineChartData(
        currentAnalytics.monthlyStats.map(stat => new Date(stat.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })),
        [{
          label: 'Revenue',
          data: currentAnalytics.monthlyStats.map(stat => stat.totalRevenue),
          color: chartColors.primary,
          fill: true,
        }]
      );
    }

    if (selectedPeriod === 'last5Years' && currentAnalytics.yearlyStats) {
      return createLineChartData(
        currentAnalytics.yearlyStats.map(stat => stat.year.toString()),
        [{
          label: 'Revenue',
          data: currentAnalytics.yearlyStats.map(stat => stat.totalRevenue),
          color: chartColors.primary,
          fill: true,
        }]
      );
    }

    return null;
  };

  // Create orders count chart data
  const createOrdersCountData = () => {
    if (!currentAnalytics) return null;

    if (selectedPeriod === 'lastDay' && currentAnalytics.dailyStats) {
      return createBarChartData(
        currentAnalytics.dailyStats.map(stat => new Date(stat.date).toLocaleDateString()),
        [{
          label: 'Orders',
          data: currentAnalytics.dailyStats.map(stat => stat.totalOrders),
          colors: [chartColors.secondary],
        }]
      );
    }

    if (selectedPeriod === 'lastMonth' && currentAnalytics.dailyStats) {
      return createBarChartData(
        currentAnalytics.dailyStats.map(stat => new Date(stat.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
        [{
          label: 'Orders',
          data: currentAnalytics.dailyStats.map(stat => stat.totalOrders),
          colors: [chartColors.secondary],
        }]
      );
    }

    if (selectedPeriod === 'lastYear' && currentAnalytics.monthlyStats) {
      return createBarChartData(
        currentAnalytics.monthlyStats.map(stat => new Date(stat.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })),
        [{
          label: 'Orders',
          data: currentAnalytics.monthlyStats.map(stat => stat.totalOrders),
          colors: [chartColors.secondary],
        }]
      );
    }

    if (selectedPeriod === 'last5Years' && currentAnalytics.yearlyStats) {
      return createBarChartData(
        currentAnalytics.yearlyStats.map(stat => stat.year.toString()),
        [{
          label: 'Orders',
          data: currentAnalytics.yearlyStats.map(stat => stat.totalOrders),
          colors: [chartColors.secondary],
        }]
      );
    }

    return null;
  };

  // Create status distribution chart data
  const createStatusDistributionData = () => {
    if (!currentAnalytics) return null;

    const statusData = [
      { label: 'Completed', value: currentAnalytics.completedOrders, color: chartColors.success },
      { label: 'Pending', value: currentAnalytics.pendingOrders, color: chartColors.warning },
      { label: 'Processing', value: currentAnalytics.processingOrders, color: chartColors.info },
      { label: 'Cancelled', value: currentAnalytics.cancelledOrders, color: chartColors.danger },
      { label: 'Refunded', value: currentAnalytics.refundedOrders, color: chartColors.dark },
    ].filter(item => item.value > 0);

    return createPieChartData(
      statusData.map(item => item.label),
      statusData.map(item => item.value),
      statusData.map(item => item.color)
    );
  };

  // Get period label
  const getPeriodLabel = () => {
    switch (selectedPeriod) {
      case 'lastDay':
        return 'Last 24 Hours';
      case 'lastMonth':
        return 'Last 30 Days';
      case 'lastYear':
        return 'Last 12 Months';
      case 'last5Years':
        return 'Last 5 Years';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FiBarChart className="text-main-color" size={24} />
          <h2 className="text-2xl font-bold text-gray-900">Order Analytics</h2>
        </div>
        <div className="flex items-center gap-4">
          {/* Period Selector */}
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
          >
            <option value="lastDay">Last 24 Hours</option>
            <option value="lastMonth">Last 30 Days</option>
            <option value="lastYear">Last 12 Months</option>
            <option value="last5Years">Last 5 Years</option>
          </select>
          <button
            onClick={fetchAnalytics}
            disabled={isLoading}
            className="inline-flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50"
          >
            <FiRefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
          <span className="ml-2 text-gray-600">Loading analytics...</span>
        </div>
      )}

      {/* Analytics Content */}
      {!isLoading && currentAnalytics && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Revenue</p>
                  <h2 className="text-2xl font-bold mt-2">{formatCurrency(currentAnalytics.totalRevenue)}</h2>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <FiDollarSign className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">{getPeriodLabel()}</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Orders</p>
                  <h2 className="text-2xl font-bold mt-2">{formatNumber(currentAnalytics.totalOrders)}</h2>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <FiShoppingBag className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">{getPeriodLabel()}</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Average Order Value</p>
                  <h2 className="text-2xl font-bold mt-2">{formatCurrency(currentAnalytics.averageOrderValue)}</h2>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <FiTrendingUp className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">{getPeriodLabel()}</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Completed Orders</p>
                  <h2 className="text-2xl font-bold mt-2">{formatNumber(currentAnalytics.completedOrders)}</h2>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <FiUsers className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">
                  {((currentAnalytics.completedOrders / currentAnalytics.totalOrders) * 100).toFixed(1)}% completion rate
                </span>
              </div>
            </div>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trend Chart */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
              {createRevenueTrendData() ? (
                <LineChart
                  data={createRevenueTrendData()!}
                  height={300}
                />
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  No data available
                </div>
              )}
            </div>

            {/* Orders Count Chart */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Orders Count</h3>
              {createOrdersCountData() ? (
                <BarChart
                  data={createOrdersCountData()!}
                  height={300}
                />
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  No data available
                </div>
              )}
            </div>
          </div>

          {/* Status Distribution and Order Status Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Status Distribution Chart */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Status Distribution</h3>
              {createStatusDistributionData() ? (
                <DoughnutChart
                  data={createStatusDistributionData()!}
                  height={300}
                  centerText={`${formatNumber(currentAnalytics.totalOrders)} Orders`}
                />
              ) : (
                <div className="flex items-center justify-center h-[300px] text-gray-500">
                  No data available
                </div>
              )}
            </div>

            {/* Order Status Breakdown */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Status Breakdown</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="font-medium text-green-800">Completed</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-green-800">{formatNumber(currentAnalytics.completedOrders)}</div>
                    <div className="text-sm text-green-600">
                      {((currentAnalytics.completedOrders / currentAnalytics.totalOrders) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <span className="font-medium text-yellow-800">Pending</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-yellow-800">{formatNumber(currentAnalytics.pendingOrders)}</div>
                    <div className="text-sm text-yellow-600">
                      {((currentAnalytics.pendingOrders / currentAnalytics.totalOrders) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="font-medium text-blue-800">Processing</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-blue-800">{formatNumber(currentAnalytics.processingOrders)}</div>
                    <div className="text-sm text-blue-600">
                      {((currentAnalytics.processingOrders / currentAnalytics.totalOrders) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span className="font-medium text-red-800">Cancelled</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-red-800">{formatNumber(currentAnalytics.cancelledOrders)}</div>
                    <div className="text-sm text-red-600">
                      {((currentAnalytics.cancelledOrders / currentAnalytics.totalOrders) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                    <span className="font-medium text-gray-800">Refunded</span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-800">{formatNumber(currentAnalytics.refundedOrders)}</div>
                    <div className="text-sm text-gray-600">
                      {((currentAnalytics.refundedOrders / currentAnalytics.totalOrders) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default OrderAnalytics;
