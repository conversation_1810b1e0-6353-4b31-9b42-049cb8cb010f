"use client";

import { useState } from "react";
import { ProductListing } from "@/services/api";
import { FiFileText, FiChevronLeft, FiChevronRight } from "react-icons/fi";

interface ProductListingsViewerProps {
  listings: ProductListing[];
}

const ProductListingsViewer = ({ listings }: ProductListingsViewerProps) => {
  const [activeIndex, setActiveIndex] = useState(0);

  const handlePrevious = () => {
    setActiveIndex((prev) => (prev === 0 ? listings.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === listings.length - 1 ? 0 : prev + 1));
  };

  if (!listings || listings.length === 0) {
    return null;
  }

  const activeListing = listings[activeIndex];

  return (
    <div className="bg-white border rounded-lg overflow-hidden shadow-sm">
      {/* Tabs navigation */}
      <div className="flex overflow-x-auto bg-gray-50 border-b scrollbar-hide">
        {listings.map((listing, index) => (
          <button
            key={listing.id}
            onClick={() => setActiveIndex(index)}
            className={`px-4 py-3 whitespace-nowrap flex items-center text-sm font-medium transition-colors ${
              index === activeIndex
                ? "text-main-color border-b-2 border-main-color bg-white"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            }`}
          >
            <FiFileText className="mr-2" size={16} />
            {listing.title}
          </button>
        ))}
      </div>

      {/* Content area */}
      <div className="p-5">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <FiFileText className="mr-2 text-main-color" size={18} />
            {activeListing.title}
          </h3>

          {listings.length > 1 && (
            <div className="flex space-x-2">
              <button
                onClick={handlePrevious}
                className="p-1.5 rounded-full hover:bg-gray-100 text-gray-500 hover:text-main-color transition-colors"
                aria-label="Previous listing"
              >
                <FiChevronLeft size={20} />
              </button>
              <button
                onClick={handleNext}
                className="p-1.5 rounded-full hover:bg-gray-100 text-gray-500 hover:text-main-color transition-colors"
                aria-label="Next listing"
              >
                <FiChevronRight size={20} />
              </button>
            </div>
          )}
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <div
            className="prose prose-sm max-w-none text-gray-700 rich-text-content"
            dangerouslySetInnerHTML={{ __html: activeListing.content }}
          />
        </div>

        {/* Pagination indicator */}
        {listings.length > 1 && (
          <div className="mt-6 flex justify-center">
            <div className="flex space-x-2">
              {listings.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`w-2.5 h-2.5 rounded-full transition-all ${
                    index === activeIndex
                      ? "bg-main-color scale-125"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                  aria-label={`Go to listing ${index + 1}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductListingsViewer;
