import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_PATH_PREFIX = '/api/tax';

// GET handler for fetching tax settings
export async function GET(request: NextRequest) {
  try {
    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');

    // Add cache-busting query parameter
    const cacheBuster = `?_=${Date.now()}`;
    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}${cacheBuster}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        ...(authHeader ? { 'Authorization': authHeader } : {})
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch tax settings' },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Set cache control headers in the response
    const headers = new Headers();
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');

    return NextResponse.json(data, {
      headers: headers
    });
  } catch (error) {
    console.error('API proxy error (GET /tax):', error);
    return NextResponse.json(
      { error: 'Failed to fetch tax settings' },
      { status: 500 }
    );
  }
}

// PUT handler for updating tax settings
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      );
    }

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to update tax settings' },
        { status: response.status }
      );
    }

    const data = await response.json();

    // Set cache control headers in the response
    const headers = new Headers();
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');

    return NextResponse.json(data, {
      headers: headers
    });
  } catch (error) {
    console.error('API proxy error (PUT /tax):', error);
    return NextResponse.json(
      { error: 'Failed to update tax settings' },
      { status: 500 }
    );
  }
}
