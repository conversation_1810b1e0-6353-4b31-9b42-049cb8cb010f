"use client";

import { useState, useEffect } from "react";
import { useSectionStore } from "@/hooks/useSectionStore";
import { useProductStore } from "@/hooks/useProductStore";
import { Product } from "@/services/api";
import { FiSearch, FiX } from "react-icons/fi";
import LoadingSpinner from "../common/LoadingSpinner";

interface AddProductsToSectionModalProps {
  isOpen: boolean;
  sectionId: number;
  onClose: () => void;
  existingProductIds: number[];
}

const AddProductsToSectionModal = ({
  isOpen,
  sectionId,
  onClose,
  existingProductIds,
}: AddProductsToSectionModalProps) => {
  const { addProductToSection } = useSectionStore();
  const { products, fetchProducts, isLoading: isLoadingProducts } = useProductStore();
  const [selectedProductIds, setSelectedProductIds] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchProducts();
      setSelectedProductIds([]);
      setSearchTerm("");
      setError(null);
    }
  }, [isOpen, fetchProducts]);

  const handleToggleProduct = (productId: number) => {
    setSelectedProductIds((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProductIds.length === availableProducts.length) {
      setSelectedProductIds([]);
    } else {
      setSelectedProductIds(availableProducts.map((p) => p.id!));
    }
  };

  const handleSubmit = async () => {
    if (selectedProductIds.length === 0) {
      setError("Please select at least one product");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Add products one by one since the store method only handles single products
      for (let i = 0; i < selectedProductIds.length; i++) {
        const productId = selectedProductIds[i];
        await addProductToSection(sectionId, productId, i); // position is the index
      }
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to add products to section");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter out products that are already in the section
  const availableProducts = products
    .filter((product) => !existingProductIds.includes(product.id!))
    .filter((product) =>
      searchTerm
        ? product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.sku.toLowerCase().includes(searchTerm.toLowerCase())
        : true
    );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">
          &#8203;
        </span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">Add Products to Section</h3>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <FiX size={20} />
                  </button>
                </div>

                <div className="mt-4">
                  <div className="mb-4 flex justify-between items-center">
                    <div className="relative w-64">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiSearch className="text-gray-400" />
                      </div>
                      <input
                        type="text"
                        placeholder="Search by name or SKU"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-3 py-2 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-main-color focus:border-main-color"
                      />
                    </div>
                    <button
                      onClick={handleSelectAll}
                      className="text-sm text-main-color hover:text-main-color/80"
                      disabled={availableProducts.length === 0}
                    >
                      {selectedProductIds.length === availableProducts.length && availableProducts.length > 0
                        ? "Deselect All"
                        : "Select All"}
                    </button>
                  </div>

                  {error && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm text-red-600">{error}</p>
                    </div>
                  )}

                  {isLoadingProducts ? (
                    <div className="flex justify-center items-center h-64">
                      <LoadingSpinner size="lg" />
                    </div>
                  ) : availableProducts.length === 0 ? (
                    <div className="text-center py-12 bg-gray-50 rounded-lg">
                      <p className="text-gray-500">
                        {searchTerm
                          ? "No products match your search criteria."
                          : "All products are already in this section."}
                      </p>
                    </div>
                  ) : (
                    <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-md">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                              <span className="sr-only">Select</span>
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                              Image
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              SKU
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Name
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Price
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {availableProducts.map((product) => (
                            <tr
                              key={product.id}
                              className={`hover:bg-gray-50 cursor-pointer ${
                                selectedProductIds.includes(product.id!) ? "bg-blue-50" : ""
                              }`}
                              onClick={() => handleToggleProduct(product.id!)}
                            >
                              <td className="px-6 py-4 whitespace-nowrap">
                                <input
                                  type="checkbox"
                                  checked={selectedProductIds.includes(product.id!)}
                                  onChange={() => handleToggleProduct(product.id!)}
                                  className="h-4 w-4 text-main-color focus:ring-main-color border-gray-300 rounded"
                                  onClick={(e) => e.stopPropagation()}
                                />
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100 border border-gray-200">
                                  {product.images && product.images.length > 0 ? (
                                    <img
                                      src={product.images[0].url}
                                      alt={product.name}
                                      className="h-full w-full object-cover"
                                    />
                                  ) : (
                                    <div className="h-full w-full flex items-center justify-center text-gray-400 text-xs">
                                      No image
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {product.sku}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {product.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                ${parseFloat(product.price).toFixed(2)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-main-color text-base font-medium text-white hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color sm:ml-3 sm:w-auto sm:text-sm"
              onClick={handleSubmit}
              disabled={isSubmitting || selectedProductIds.length === 0}
            >
              {isSubmitting ? <LoadingSpinner size="sm" /> : `Add ${selectedProductIds.length} Products`}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddProductsToSectionModal;
