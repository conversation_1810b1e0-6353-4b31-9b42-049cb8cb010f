import { create } from "zustand";
import { taxApi, TaxSettings } from "@/services/taxApi";

interface TaxState {
  taxSettings: TaxSettings | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchTaxSettings: () => Promise<TaxSettings | null>;
  updateTaxSettings: (settings: Partial<TaxSettings>) => Promise<TaxSettings | null>;
  clearError: () => void;
}

export const useTaxStore = create<TaxState>((set) => ({
  taxSettings: null,
  isLoading: false,
  error: null,

  clearError: () => set({ error: null }),

  fetchTaxSettings: async () => {
    set({ isLoading: true, error: null });
    try {
      const taxSettings = await taxApi.getTaxSettings();
      set({ taxSettings, isLoading: false });
      return taxSettings;
    } catch (error) {
      console.error('Store error - fetchTaxSettings:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch tax settings',
        isLoading: false,
      });
      return null;
    }
  },

  updateTaxSettings: async (settings: Partial<TaxSettings>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedSettings = await taxApi.updateTaxSettings(settings);
      set({ taxSettings: updatedSettings, isLoading: false });
      return updatedSettings;
    } catch (error) {
      console.error('Store error - updateTaxSettings:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to update tax settings',
        isLoading: false,
      });
      return null;
    }
  },
}));

export default useTaxStore;
