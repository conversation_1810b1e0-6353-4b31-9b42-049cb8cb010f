// Orders API service for admin dashboard

import {
  Order,
  OrdersResponse,
  OrderAnalyticsWithStats,
  OrderReports,
  OrderInsights
} from './api';

// Get the API base URL from environment variables (same pattern as other APIs)
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const ORDERS_API_BASE = `${API_BASE_URL}/api/orders`;

// Log the orders API configuration for debugging
console.log('Orders API Base URL:', API_BASE_URL);
console.log('Orders API Full URL:', ORDERS_API_BASE);

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  if (!response.ok) {
    if (response.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('accessToken');
      window.location.href = '/admin';
      throw new Error('Session expired. Please login again.');
    }
    if (response.status === 403) {
      throw new Error('Insufficient permissions to access orders.');
    }
    const errorText = await response.text();
    throw new Error(`API Error: ${response.status} - ${errorText}`);
  }
  return response.json();
};

// Helper function to get auth headers
const getAuthHeaders = (): HeadersInit => {
  const token = localStorage.getItem('accessToken');
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

// Orders API service
export const ordersApi = {
  // Get all orders with pagination and filtering
  getOrders: async (params: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
    minAmount?: number;
    maxAmount?: number;
    customerId?: number;
  } = {}): Promise<OrdersResponse> => {
    try {
      const queryParams = new URLSearchParams();
      
      // Add parameters to query string
      if (params.page) queryParams.append('page', params.page.toString());
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.search) queryParams.append('search', params.search);
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.status) queryParams.append('status', params.status);
      if (params.dateFrom) queryParams.append('dateFrom', params.dateFrom);
      if (params.dateTo) queryParams.append('dateTo', params.dateTo);
      if (params.minAmount) queryParams.append('minAmount', params.minAmount.toString());
      if (params.maxAmount) queryParams.append('maxAmount', params.maxAmount.toString());
      if (params.customerId) queryParams.append('customerId', params.customerId.toString());

      const url = `${ORDERS_API_BASE}?${queryParams.toString()}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrdersResponse>(response);
    } catch (error) {
      console.error('Failed to fetch orders:', error);
      throw error;
    }
  },

  // Get order by ID
  getOrderById: async (id: number): Promise<Order> => {
    try {
      const response = await fetch(`${ORDERS_API_BASE}/${id}`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<Order>(response);
    } catch (error) {
      console.error(`Failed to fetch order ${id}:`, error);
      throw error;
    }
  },

  // Get last day analytics
  getLastDayAnalytics: async (): Promise<OrderAnalyticsWithStats> => {
    try {
      const response = await fetch(`${ORDERS_API_BASE}/analytics/last-day`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrderAnalyticsWithStats>(response);
    } catch (error) {
      console.error('Failed to fetch last day analytics:', error);
      throw error;
    }
  },

  // Get last month analytics
  getLastMonthAnalytics: async (): Promise<OrderAnalyticsWithStats> => {
    try {
      const response = await fetch(`${ORDERS_API_BASE}/analytics/last-month`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrderAnalyticsWithStats>(response);
    } catch (error) {
      console.error('Failed to fetch last month analytics:', error);
      throw error;
    }
  },

  // Get last year analytics
  getLastYearAnalytics: async (): Promise<OrderAnalyticsWithStats> => {
    try {
      const response = await fetch(`${ORDERS_API_BASE}/analytics/last-year`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrderAnalyticsWithStats>(response);
    } catch (error) {
      console.error('Failed to fetch last year analytics:', error);
      throw error;
    }
  },

  // Get last 5 years analytics
  getLast5YearsAnalytics: async (): Promise<OrderAnalyticsWithStats> => {
    try {
      const response = await fetch(`${ORDERS_API_BASE}/analytics/last-5-years`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrderAnalyticsWithStats>(response);
    } catch (error) {
      console.error('Failed to fetch last 5 years analytics:', error);
      throw error;
    }
  },

  // Get complete order reports
  getOrderReports: async (): Promise<OrderReports> => {
    try {
      const response = await fetch(`${ORDERS_API_BASE}/reports`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrderReports>(response);
    } catch (error) {
      console.error('Failed to fetch order reports:', error);
      throw error;
    }
  },

  // Get order insights with top products and customers
  getOrderInsights: async (params: {
    dateFrom?: string;
    dateTo?: string;
  } = {}): Promise<OrderInsights> => {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.dateFrom) queryParams.append('dateFrom', params.dateFrom);
      if (params.dateTo) queryParams.append('dateTo', params.dateTo);

      const url = `${ORDERS_API_BASE}/insights?${queryParams.toString()}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      return handleResponse<OrderInsights>(response);
    } catch (error) {
      console.error('Failed to fetch order insights:', error);
      throw error;
    }
  },
};
