import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Products API - Base URL:', API_BASE_URL);
console.log('Products API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// GET handler for fetching all products with pagination
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get pagination parameters
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';
    const search = searchParams.get('search') || '';

    // Build query string for backend
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('limit', limit);

    // Add search parameter if provided
    if (search) {
      queryParams.append('search', search);
    }

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/products?${queryParams.toString()}`;
    console.log(`Fetching products with pagination: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Backend API error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch products from backend' },
        { status: response.status }
      );
    }

    let data;
    try {
      data = await response.json();
      console.log(`Products API: Received ${data?.data?.length || 0} products, page ${data?.pagination?.page || 'unknown'}`);
    } catch (e) {
      console.error('Error parsing JSON response for products:', e);
      return NextResponse.json(
        { error: 'Invalid response format from backend' },
        { status: 500 }
      );
    }

    // Ensure the response has the expected structure
    if (!data || !data.data || !Array.isArray(data.data)) {
      console.error('Invalid products response format:', data);
      return NextResponse.json({
        data: [],
        pagination: {
          total: 0,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      });
    }

    // Ensure pagination values are numbers
    if (data.pagination) {
      data.pagination.page = parseInt(data.pagination.page);
      data.pagination.limit = parseInt(data.pagination.limit);
      data.pagination.total = parseInt(data.pagination.total);
      data.pagination.totalPages = parseInt(data.pagination.totalPages);
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /products):', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// POST handler for creating a new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log(`API proxy - Creating new product:`, body);

    // Always remove the ProductAttribute field for the simple product endpoints
    // Grouped products should use their dedicated endpoints
    if ('ProductAttribute' in body) {
      delete body.ProductAttribute;
      console.log(`API proxy - Removed ProductAttribute field for product creation`);
    }

    // Also remove variants field if present
    if ('variants' in body) {
      delete body.variants;
      console.log(`API proxy - Removed variants field for product creation`);
    }

    console.log(`API proxy - Cleaned request body:`, body);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /products):', error);
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    );
  }
}
