'use client';

import { useState } from 'react';
import { FiFilter, FiX, FiChevronDown, FiChevronUp } from 'react-icons/fi';

// Types
export interface FilterOptions {
  minPrice?: number;
  maxPrice?: number;
  onSale?: boolean;
  inStock?: boolean;
  categoryIds?: number[];
  tagNames?: string[];
  search?: string;
  sortBy?: string;
  minDiscountPercent?: number;
}

export interface FilterMetadata {
  totalProducts: number;
  priceRange: {
    min: number;
    max: number;
  };
  availableCategories: Array<{
    id: number;
    name: string;
    slug: string;
    count: number;
  }>;
  availableTags: Array<{
    id: number;
    name: string;
    slug: string;
    count: number;
  }>;
  stockStatusCounts: {
    inStock: number;
    outOfStock: number;
    onBackorder: number;
  };
  productTypeCounts: {
    simple: number;
    grouped: number;
    variable?: number;
    external?: number;
  };
  onSaleCount: number;
}

interface ProductFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onApplyFilters: () => void;
  isLoading?: boolean;
  metadata?: FilterMetadata;
  showCategoryFilter?: boolean;
}

const ProductFilters = ({
  filters,
  onFiltersChange,
  onApplyFilters,
  isLoading = false,
  metadata,
  showCategoryFilter = true
}: ProductFiltersProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    price: true,
    availability: true,
    categories: false,
    tags: false
  });



  const sortOptions = [
    { label: 'Newest First', value: 'newest' },
    { label: 'Price: Low to High', value: 'price_asc' },
    { label: 'Price: High to Low', value: 'price_desc' },
    { label: 'Name: A to Z', value: 'name_asc' },
    { label: 'Name: Z to A', value: 'name_desc' },
    { label: 'Popularity', value: 'popularity' },
    { label: 'Rating', value: 'rating' }
  ];

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    const newFilters = {
      ...filters,
      [key]: value
    };
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    onFiltersChange({
      sortBy: filters.sortBy || 'newest'
    });
  };

  const hasActiveFilters = Object.keys(filters).some(key => 
    key !== 'sortBy' && filters[key as keyof FilterOptions] !== undefined
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <FiFilter size={20} className="text-gray-600" />
          <h3 className="font-medium text-gray-800">Filters</h3>
          {hasActiveFilters && (
            <span className="bg-main-color text-white text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              Clear All
            </button>
          )}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="lg:hidden p-1 text-gray-500 hover:text-gray-700"
          >
            {isOpen ? <FiX size={20} /> : <FiFilter size={20} />}
          </button>
        </div>
      </div>

      {/* Filter Content */}
      <div className={`${isOpen ? 'block' : 'hidden'} lg:block`}>
        <div className="p-4 space-y-6">
          {/* Sort */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sort By
            </label>
            <select
              value={filters.sortBy || 'newest'}
              onChange={(e) => updateFilter('sortBy', e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search Products
            </label>
            <input
              type="text"
              value={filters.search || ''}
              onChange={(e) => updateFilter('search', e.target.value || undefined)}
              placeholder="Search by name, description, or SKU..."
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent"
            />
          </div>

          {/* Price Section */}
          <div>
            <button
              onClick={() => toggleSection('price')}
              className="flex items-center justify-between w-full text-sm font-medium text-gray-700 mb-2"
            >
              Price Range
              {expandedSections.price ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
            </button>

            {expandedSections.price && (
              <div className="space-y-4">
                {/* Simple Min/Max Price Inputs */}
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Minimum Price
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                      <input
                        type="number"
                        value={filters.minPrice || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          updateFilter('minPrice', value ? parseFloat(value) : undefined);
                        }}
                        placeholder="0.00"
                        min="0"
                        step="0.01"
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Maximum Price
                    </label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">$</span>
                      <input
                        type="number"
                        value={filters.maxPrice || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          updateFilter('maxPrice', value ? parseFloat(value) : undefined);
                        }}
                        placeholder="999.99"
                        min="0"
                        step="0.01"
                        className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>

                {/* Price Range Display */}
                {metadata?.priceRange && (
                  <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                    Available range: ${metadata.priceRange.min.toFixed(2)} - ${metadata.priceRange.max.toFixed(2)}
                  </div>
                )}

                {/* Quick Price Filters */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Quick Filters</label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => {
                        const newFilters = {
                          ...filters,
                          minPrice: undefined,
                          maxPrice: 50
                        };
                        onFiltersChange(newFilters);
                      }}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        filters.maxPrice === 50 && !filters.minPrice
                          ? 'bg-main-color text-white border-main-color'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-main-color'
                      }`}
                    >
                      Under $50
                    </button>
                    <button
                      onClick={() => {
                        const newFilters = {
                          ...filters,
                          minPrice: 50,
                          maxPrice: 100
                        };
                        onFiltersChange(newFilters);
                      }}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        filters.minPrice === 50 && filters.maxPrice === 100
                          ? 'bg-main-color text-white border-main-color'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-main-color'
                      }`}
                    >
                      $50 - $100
                    </button>
                    <button
                      onClick={() => {
                        const newFilters = {
                          ...filters,
                          minPrice: 100,
                          maxPrice: 200
                        };
                        onFiltersChange(newFilters);
                      }}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        filters.minPrice === 100 && filters.maxPrice === 200
                          ? 'bg-main-color text-white border-main-color'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-main-color'
                      }`}
                    >
                      $100 - $200
                    </button>
                    <button
                      onClick={() => {
                        const newFilters = {
                          ...filters,
                          minPrice: 200,
                          maxPrice: undefined
                        };
                        onFiltersChange(newFilters);
                      }}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        filters.minPrice === 200 && !filters.maxPrice
                          ? 'bg-main-color text-white border-main-color'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-main-color'
                      }`}
                    >
                      $200+
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Availability Section */}
          <div>
            <button
              onClick={() => toggleSection('availability')}
              className="flex items-center justify-between w-full text-sm font-medium text-gray-700 mb-2"
            >
              Availability
              {expandedSections.availability ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
            </button>

            {expandedSections.availability && (
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.inStock === true}
                    onChange={(e) => updateFilter('inStock', e.target.checked ? true : undefined)}
                    className="mr-2 text-main-color focus:ring-main-color"
                  />
                  <span className="text-sm text-gray-600">
                    In Stock Only
                    {metadata?.stockStatusCounts && (
                      <span className="text-gray-400 ml-1">({metadata.stockStatusCounts.inStock})</span>
                    )}
                  </span>
                </label>
{/* 
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.onSale === true}
                    onChange={(e) => updateFilter('onSale', e.target.checked ? true : undefined)}
                    className="mr-2 text-main-color focus:ring-main-color"
                  />
                  <span className="text-sm text-gray-600">
                    On Sale Only
                    {metadata?.onSaleCount && (
                      <span className="text-gray-400 ml-1">({metadata.onSaleCount})</span>
                    )}
                  </span>
                </label> */}

                {/* Discount Percentage
                <div>
                  <label className="block text-xs text-gray-500 mb-1">Min Discount %</label>
                  <input
                    type="number"
                    value={filters.minDiscountPercent || ''}
                    onChange={(e) => updateFilter('minDiscountPercent', e.target.value ? Number(e.target.value) : undefined)}
                    placeholder="0"
                    min="0"
                    max="100"
                    className="w-full border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-1 focus:ring-main-color"
                  />
                </div> */}
              </div>
            )}
          </div>

          {/* Categories Section */}
          {showCategoryFilter && metadata?.availableCategories && metadata.availableCategories.length > 0 && (
            <div>
              <button
                onClick={() => toggleSection('categories')}
                className="flex items-center justify-between w-full text-sm font-medium text-gray-700 mb-2"
              >
                Categories
                {expandedSections.categories ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
              </button>

              {expandedSections.categories && (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {metadata.availableCategories.map(category => (
                    <label key={category.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.categoryIds?.includes(category.id) || false}
                        onChange={(e) => {
                          const currentIds = filters.categoryIds || [];
                          if (e.target.checked) {
                            updateFilter('categoryIds', [...currentIds, category.id]);
                          } else {
                            updateFilter('categoryIds', currentIds.filter(id => id !== category.id));
                          }
                        }}
                        className="mr-2 text-main-color focus:ring-main-color"
                      />
                      <span className="text-sm text-gray-600">
                        {category.name}
                        <span className="text-gray-400 ml-1">({category.count})</span>
                      </span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Tags Section */}
          {metadata?.availableTags && metadata.availableTags.length > 0 && (
            <div>
              <button
                onClick={() => toggleSection('tags')}
                className="flex items-center justify-between w-full text-sm font-medium text-gray-700 mb-2"
              >
                Tags
                {expandedSections.tags ? <FiChevronUp size={16} /> : <FiChevronDown size={16} />}
              </button>

              {expandedSections.tags && (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {metadata.availableTags.map(tag => (
                    <label key={tag.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.tagNames?.includes(tag.name) || false}
                        onChange={(e) => {
                          const currentTags = filters.tagNames || [];
                          if (e.target.checked) {
                            updateFilter('tagNames', [...currentTags, tag.name]);
                          } else {
                            updateFilter('tagNames', currentTags.filter(name => name !== tag.name));
                          }
                        }}
                        className="mr-2 text-main-color focus:ring-main-color"
                      />
                      <span className="text-sm text-gray-600">
                        {tag.name}
                        <span className="text-gray-400 ml-1">({tag.count})</span>
                      </span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          )}


        </div>

        {/* Apply Button */}
        <div className="p-4 border-t bg-gray-50">
          <button
            onClick={onApplyFilters}
            disabled={isLoading}
            className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
              isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-main-color text-white hover:bg-main-color/90'
            }`}
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Applying...
              </div>
            ) : (
              'Apply Filters'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductFilters;
