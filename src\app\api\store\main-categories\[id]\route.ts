import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// GET handler for fetching a main category by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/main-categories/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Main category with ID ${id} not found` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (GET /main-categories/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to fetch main category with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// PATCH handler for updating a main category
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/main-categories/${id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to update main category with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (PATCH /main-categories/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to update main category with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// DELETE handler for deleting a main category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/main-categories/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to delete main category with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (DELETE /main-categories/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to delete main category with ID ${params.id}` },
      { status: 500 }
    );
  }
}
