"use client";

import AdminSidebar from "@/components/admin/AdminSidebar";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/hooks/useAuthStore";
import LoadingSpinner from "@/components/shop/product/LoadingSpinner";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const { isAuthenticated, getProfile, user, isLoading } = useAuthStore();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true);

      // Check if user is authenticated
      if (!isAuthenticated()) {
        router.push('/admin');
        return;
      }

      // If authenticated but no user data, fetch profile
      if (!user) {
        const userData = await getProfile();

        // If no user data or not an admin, redirect
        if (!userData || (!userData.isAdmin && !userData.isSuperAdmin)) {
          router.push('/admin');
          return;
        }
      } else if (!user.isAdmin && !user.isSuperAdmin) {
        // If user data exists but not an admin, redirect
        router.push('/admin');
        return;
      }

      setIsCheckingAuth(false);
    };

    checkAuth();
  }, [router, isAuthenticated, getProfile, user]);

  if (isCheckingAuth || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />
      <div className="ml-64 p-8">{children}</div>
    </div>
  );
}

