"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  // Add other fields as needed
}

export default function TestProductPage({ params }: { params: { id: string } }) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        console.log(`Test Page - Fetching product with ID: ${params.id}`);
        
        // Use our test API endpoint
        const response = await fetch(`/api/test/product/${params.id}`, {
          cache: 'no-store'
        });

        if (!response.ok) {
          console.error(`Test Page - Product not found: ${params.id}`);
          setError(`Product not found: ${params.id}`);
          setLoading(false);
          return;
        }

        const data = await response.json();
        console.log('Test Page - Product data received:', data);
        setProduct(data);
      } catch (err) {
        console.error('Test Page - Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [params.id]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error || !product) {
    return (
      <div>
        <h1>Error Loading Product</h1>
        <p>{error || 'Product not found'}</p>
        <button onClick={() => router.push('/')}>Go Home</button>
      </div>
    );
  }

  return (
    <div>
      <h1>Test Product Page</h1>
      <h2>{product.name}</h2>
      <p>ID: {product.id}</p>
      <p>Slug: {product.slug}</p>
      <p>Price: ${product.price}</p>
      <div dangerouslySetInnerHTML={{ __html: product.description }} />
      <button onClick={() => router.push(`/shop/${product.slug}`)}>
        View in Shop
      </button>
    </div>
  );
}
