"use client";

import { useEffect, useState } from "react";
import { useMainCategoryStore } from "@/hooks/useMainCategoryStore";
import { FiArrowLeft, FiEdit2, FiPlus, FiImage } from "react-icons/fi";
import LoadingSpinner from "@/components/LoadingSpinner";
import Image from "next/image";

interface MainCategoryCategoriesViewProps {
  mainCategoryId: number;
  onBack: () => void;
  onAddCategories: () => void;
}

const MainCategoryCategoriesView = ({
  mainCategoryId,
  onBack,
  onAddCategories
}: MainCategoryCategoriesViewProps) => {
  const { mainCategories, isLoading, error, fetchMainCategories } = useMainCategoryStore();
  const [mainCategory, setMainCategory] = useState<typeof mainCategories[0] | null>(null);

  useEffect(() => {
    const loadData = async () => {
      await fetchMainCategories();
    };

    loadData();
  }, [fetchMainCategories]);

  useEffect(() => {
    if (mainCategories.length > 0) {
      const category = mainCategories.find(c => c.id === mainCategoryId);
      setMainCategory(category || null);
    }
  }, [mainCategories, mainCategoryId]);

  if (isLoading && !mainCategory) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>Error loading main category: {error}</p>
        <button
          onClick={() => fetchMainCategories()}
          className="mt-2 bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!mainCategory) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">Main category not found.</p>
        <button
          onClick={onBack}
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="mr-4 p-2 rounded-full hover:bg-gray-100"
          title="Go Back"
        >
          <FiArrowLeft size={20} />
        </button>
        <h2 className="text-xl font-semibold">
          Categories in "{mainCategory.name}"
        </h2>
        <button
          onClick={onAddCategories}
          className="ml-auto flex items-center gap-1 bg-main-color text-white px-3 py-1.5 rounded-md hover:bg-main-color/90"
        >
          <FiPlus size={16} />
          <span>Add Categories</span>
        </button>
      </div>

      {mainCategory.categories && mainCategory.categories.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Image
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Slug
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mainCategory.categories.map((category) => (
                <tr key={category.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {category.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {category.imageUrl ? (
                      <div className="relative h-10 w-10 rounded-md overflow-hidden">
                        <Image
                          src={category.imageUrl}
                          alt={category.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : (
                      <FiImage className="h-10 w-10 text-gray-300 p-2 border border-gray-200 rounded-md" />
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {category.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {category.slug}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {category.description}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-500">No categories in this main category.</p>
          <button
            onClick={onAddCategories}
            className="mt-4 flex items-center gap-1 bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 mx-auto"
          >
            <FiPlus size={16} />
            <span>Add Categories</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default MainCategoryCategoriesView;
