import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_AUTH_PATH_PREFIX = '/api/auth';

// Log the API configuration to help with debugging
console.log('Auth API - Base URL:', API_BASE_URL);
console.log('Auth API - Path Prefix:', API_AUTH_PATH_PREFIX);

// POST handler for creating an admin user
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];
    const body = await request.json();

    console.log(`API proxy - Creating admin user with email: ${body.email}`);

    const response = await fetch(`${API_BASE_URL}${API_AUTH_PATH_PREFIX}/admin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    // Return the response with the same status code
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /auth/admin):', error);
    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}
