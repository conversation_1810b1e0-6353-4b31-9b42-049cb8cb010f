"use client";

import { useState } from "react";
import { ProductListing } from "@/services/api";
import { FiPlus, FiX, FiEdit2, FiTrash2, FiFileText } from "react-icons/fi";
import RichTextEditor from "../common/RichTextEditor";

interface ListingsManagerProps {
  listings: ProductListing[];
  onChange: (listings: ProductListing[], deleteListingIds?: number[]) => void;
}

const ListingsManager = ({ listings, onChange }: ListingsManagerProps) => {
  const [isAddingListing, setIsAddingListing] = useState(false);
  const [isEditingListing, setIsEditingListing] = useState<number | null>(null);
  const [newListing, setNewListing] = useState<Omit<ProductListing, 'id'>>({
    title: "",
    content: "",
  });
  const [deleteListingIds, setDeleteListingIds] = useState<number[]>([]);

  const handleAddListing = () => {
    if (!newListing.title.trim()) {
      alert("Please enter a title for the listing");
      return;
    }

    const updatedListings = [
      ...listings,
      { ...newListing, id: Math.random() * -1000 } // Temporary negative ID for new listings
    ];
    
    onChange(updatedListings, deleteListingIds);
    setNewListing({ title: "", content: "" });
    setIsAddingListing(false);
  };

  const handleUpdateListing = (index: number) => {
    if (!listings[index].title.trim()) {
      alert("Please enter a title for the listing");
      return;
    }

    setIsEditingListing(null);
  };

  const handleDeleteListing = (index: number) => {
    const listingToDelete = listings[index];
    const updatedListings = listings.filter((_, i) => i !== index);
    
    // If the listing has a real ID (not a temporary one), add it to the delete list
    if (listingToDelete.id && listingToDelete.id > 0) {
      setDeleteListingIds([...deleteListingIds, listingToDelete.id]);
    }
    
    onChange(updatedListings, [...deleteListingIds, ...(listingToDelete.id && listingToDelete.id > 0 ? [listingToDelete.id] : [])]);
  };

  const handleListingChange = (index: number, field: keyof ProductListing, value: string) => {
    const updatedListings = [...listings];
    updatedListings[index] = {
      ...updatedListings[index],
      [field]: value
    };
    
    onChange(updatedListings, deleteListingIds);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Additional Information</h3>
        {!isAddingListing && (
          <button
            type="button"
            onClick={() => setIsAddingListing(true)}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
          >
            <FiPlus className="mr-2" />
            Add Information
          </button>
        )}
      </div>

      {/* Existing Listings */}
      {listings.length > 0 && (
        <div className="space-y-4">
          {listings.map((listing, index) => (
            <div key={listing.id || `new-${index}`} className="border rounded-lg overflow-hidden bg-white">
              <div className="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
                <div className="flex items-center">
                  <FiFileText className="text-gray-500 mr-2" />
                  {isEditingListing === index ? (
                    <input
                      type="text"
                      value={listing.title}
                      onChange={(e) => handleListingChange(index, 'title', e.target.value)}
                      className="border-gray-300 rounded-md shadow-sm focus:border-main-color focus:ring focus:ring-main-color focus:ring-opacity-50"
                      placeholder="Enter title"
                    />
                  ) : (
                    <h4 className="font-medium text-gray-800">{listing.title}</h4>
                  )}
                </div>
                <div className="flex space-x-2">
                  {isEditingListing === index ? (
                    <button
                      type="button"
                      onClick={() => handleUpdateListing(index)}
                      className="text-green-600 hover:text-green-800"
                    >
                      Save
                    </button>
                  ) : (
                    <button
                      type="button"
                      onClick={() => setIsEditingListing(index)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <FiEdit2 />
                    </button>
                  )}
                  <button
                    type="button"
                    onClick={() => handleDeleteListing(index)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <FiTrash2 />
                  </button>
                </div>
              </div>
              <div className="p-4">
                {isEditingListing === index ? (
                  <RichTextEditor
                    value={listing.content}
                    onChange={(value) => handleListingChange(index, 'content', value)}
                    placeholder="Enter content..."
                    height="150px"
                  />
                ) : (
                  <div 
                    className="prose prose-sm max-w-none text-gray-700 rich-text-content"
                    dangerouslySetInnerHTML={{ __html: listing.content }}
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add New Listing Form */}
      {isAddingListing && (
        <div className="border rounded-lg overflow-hidden bg-white">
          <div className="bg-gray-50 px-4 py-3 border-b flex justify-between items-center">
            <div className="flex items-center">
              <FiFileText className="text-gray-500 mr-2" />
              <input
                type="text"
                value={newListing.title}
                onChange={(e) => setNewListing({ ...newListing, title: e.target.value })}
                className="border-gray-300 rounded-md shadow-sm focus:border-main-color focus:ring focus:ring-main-color focus:ring-opacity-50"
                placeholder="Enter title"
              />
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleAddListing}
                className="text-green-600 hover:text-green-800"
              >
                Save
              </button>
              <button
                type="button"
                onClick={() => {
                  setIsAddingListing(false);
                  setNewListing({ title: "", content: "" });
                }}
                className="text-gray-600 hover:text-gray-800"
              >
                <FiX />
              </button>
            </div>
          </div>
          <div className="p-4">
            <RichTextEditor
              value={newListing.content}
              onChange={(value) => setNewListing({ ...newListing, content: value })}
              placeholder="Enter content..."
              height="150px"
            />
          </div>
        </div>
      )}

      {listings.length === 0 && !isAddingListing && (
        <div className="text-center py-6 bg-gray-50 rounded-lg border border-dashed border-gray-300">
          <FiFileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No additional information</h3>
          <p className="mt-1 text-sm text-gray-500">Add information sections like specifications, care instructions, etc.</p>
          <div className="mt-6">
            <button
              type="button"
              onClick={() => setIsAddingListing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
            >
              <FiPlus className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
              Add Information
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ListingsManager;
