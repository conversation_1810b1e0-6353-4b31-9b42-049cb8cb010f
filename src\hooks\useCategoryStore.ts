import { create } from "zustand";
import { Category, categoryApi } from "@/services/api";

interface CategoryState {
  categories: Category[];
  isLoading: boolean;
  error: string | null;
  selectedCategory: Category | null;

  // Actions
  fetchCategories: () => Promise<Category[]>;
  createCategory: (category: Category) => Promise<Category | null>;
  updateCategory: (id: number, category: Partial<Category>) => Promise<Category | null>;
  deleteCategory: (id: number) => Promise<Category | null>;
  setSelectedCategory: (category: Category | null) => void;
  clearError: () => void;
}

export const useCategoryStore = create<CategoryState>((set) => ({
  categories: [],
  isLoading: false,
  error: null,
  selectedCategory: null,

  clearError: () => set({ error: null }),

  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      const categories = await categoryApi.getAll();
      // Ensure categories is a valid array
      if (!Array.isArray(categories)) {
        console.error('Invalid categories format:', categories);
        throw new Error('Invalid categories format from API');
      }
      set({ categories, isLoading: false });
      return categories;
    } catch (error) {
      console.error('Store error - fetchCategories:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch categories',
        isLoading: false,
        categories: [] // Reset categories on error
      });
      return [];
    }
  },

  createCategory: async (category: Category) => {
    set({ isLoading: true, error: null });
    try {
      const newCategory = await categoryApi.create(category);
      set(state => ({
        categories: [...state.categories, newCategory],
        isLoading: false
      }));
      return newCategory;
    } catch (error) {
      console.error('Store error - createCategory:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create category',
        isLoading: false
      });
      return null;
    }
  },

  updateCategory: async (id: number, category: Partial<Category>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedCategory = await categoryApi.update(id, category);
      set(state => ({
        categories: state.categories.map(c =>
          c.id === id ? { ...c, ...updatedCategory } : c
        ),
        isLoading: false
      }));
      return updatedCategory;
    } catch (error) {
      console.error(`Store error - updateCategory (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update category with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  deleteCategory: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const deletedCategory = await categoryApi.delete(id);
      set(state => ({
        categories: state.categories.filter(c => c.id !== id),
        isLoading: false
      }));
      return deletedCategory;
    } catch (error) {
      console.error(`Store error - deleteCategory (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete category with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  setSelectedCategory: (category: Category | null) => {
    set({ selectedCategory: category });
  }
}));
