"use client";

import { useState, useEffect } from "react";
import { ProductAttribute, groupedProductApi } from "@/services/api";
import { FiPlus, FiAlertTriangle } from "react-icons/fi";

interface AttributeStepProps {
  selectedAttributes: ProductAttribute[];
  onAttributesChange: (attributes: ProductAttribute[]) => void;
}

const AttributeStep = ({ selectedAttributes, onAttributesChange }: AttributeStepProps) => {
  const [attributes, setAttributes] = useState<ProductAttribute[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // New attribute form
  const [newAttributeName, setNewAttributeName] = useState("");

  // Fetch all available attributes
  useEffect(() => {
    fetchAttributes();
  }, []);

  const fetchAttributes = async () => {
    try {
      setIsLoading(true);
      const fetchedAttributes = await groupedProductApi.getAttributes();
      setAttributes(fetchedAttributes);
      setError(null);
    } catch (error) {
      console.error("Failed to fetch attributes:", error);
      setError("Failed to load product attributes");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle attribute selection
  const handleAttributeSelection = (attribute: ProductAttribute) => {
    const isSelected = selectedAttributes.some(attr => attr.id === attribute.id);

    if (isSelected) {
      // Remove from selection
      onAttributesChange(selectedAttributes.filter(attr => attr.id !== attribute.id));
    } else {
      // Add to selection
      onAttributesChange([...selectedAttributes, attribute]);
    }
  };

  // Create a new attribute
  const handleCreateAttribute = async () => {
    if (!newAttributeName.trim()) {
      setError("Attribute name is required");
      return;
    }

    try {
      setIsLoading(true);
      const newAttribute = await groupedProductApi.createAttribute({
        name: newAttributeName,
      });

      setAttributes([...attributes, newAttribute]);
      onAttributesChange([...selectedAttributes, newAttribute]);
      setNewAttributeName("");
      setError(null);
    } catch (error) {
      console.error("Failed to create attribute:", error);
      setError("Failed to create attribute");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium border-b pb-2">Select Product Attributes</h3>

      {error && (
        <div className="p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <FiAlertTriangle className="mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          Select the attributes that will define the product variants (e.g., Size, Color, Material).
        </p>

        {isLoading ? (
          <div className="py-4 text-center">Loading attributes...</div>
        ) : attributes.length === 0 ? (
          <div className="py-4 text-center text-gray-500">No attributes found. Create your first attribute below.</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {attributes.map(attribute => (
              <div
                key={attribute.id}
                className={`p-4 border rounded-md cursor-pointer transition-colors ${
                  selectedAttributes.some(attr => attr.id === attribute.id)
                    ? "border-main-color bg-main-color bg-opacity-10"
                    : "border-gray-200 hover:border-main-color"
                }`}
                onClick={() => handleAttributeSelection(attribute)}
              >
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedAttributes.some(attr => attr.id === attribute.id)}
                    onChange={() => handleAttributeSelection(attribute)}
                    className="mr-3 h-5 w-5 text-main-color rounded"
                  />
                  <div>
                    <h4 className="font-medium">{attribute.name}</h4>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-8 p-4 border border-dashed border-gray-300 rounded-md">
        <h4 className="font-medium mb-3">Add New Attribute</h4>
        <div className="flex flex-col md:flex-row gap-3">
          <input
            type="text"
            value={newAttributeName}
            onChange={e => setNewAttributeName(e.target.value)}
            placeholder="Attribute name (e.g., Size, Color)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
          />
          <button
            type="button"
            onClick={handleCreateAttribute}
            disabled={isLoading}
            className="px-4 py-2 bg-main-color text-white rounded-md flex items-center justify-center"
          >
            <FiPlus className="mr-1" /> Add Attribute
          </button>
        </div>
      </div>
    </div>
  );
};

export default AttributeStep;
