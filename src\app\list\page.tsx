import ProductList from "@/components/ProductList";
import Skeleton from "@/components/Skeleton";
// import { wixClientServer } from "@/lib/wixClientServer";
import Image from "next/image";
import { Suspense } from "react";

const ListPage = async ({ searchParams }: { searchParams: any }) => {
  // Get search query from URL
  const query = searchParams.query || '';
  const page = searchParams.page || '1';
  const limit = searchParams.limit || '20';

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 relative">
      {/* CAMPAIGN */}
      <div className="hidden bg-green-50 px-4 sm:flex justify-center items-center h-32">
        <h1 className="text-2xl font-semibold text-gray-700">
          {query ? `Results for "${query}"` : 'All Products'}
        </h1>
      </div>
      {/* FILTER */}
      {/* <Filter /> */}
      {/* PRODUCTS */}
      <Suspense fallback={<Skeleton />}>
        <ProductList

          searchParams={{
            ...searchParams,
            query,
            page,
            limit
          }}
        />
      </Suspense>
    </div>
  );
};

export default ListPage;

