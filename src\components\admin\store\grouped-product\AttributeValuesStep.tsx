"use client";

import { useState, useEffect } from "react";
import { ProductAttribute, AttributeValue } from "@/services/api";
import { FiPlus, FiTrash2, FiAlertTriangle } from "react-icons/fi";

interface AttributeValuesStepProps {
  attributes: ProductAttribute[];
  attributeValues: Record<number, AttributeValue[]>;
  onAttributeValuesChange: (attributeId: number, values: AttributeValue[]) => void;
}

const AttributeValuesStep = ({
  attributes,
  attributeValues,
  onAttributeValuesChange,
}: AttributeValuesStepProps) => {
  const [activeAttributeId, setActiveAttributeId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  // New value form
  const [newValue, setNewValue] = useState("");

  // Set the first attribute as active by default
  useEffect(() => {
    if (attributes.length > 0 && !activeAttributeId) {
      console.log(`Setting initial active attribute to ${attributes[0].id}`);
      setActiveAttributeId(attributes[0].id!);
    }
  }, [attributes, activeAttributeId]);

  // Set initial values for the active attribute if none exist
  useEffect(() => {
    if (!activeAttributeId) return;

    // If there are no values for this attribute yet, initialize with an empty array
    if (!attributeValues[activeAttributeId]) {
      onAttributeValuesChange(activeAttributeId, []);
    }
  }, [activeAttributeId, attributeValues, onAttributeValuesChange]);

  // Add a new value for the active attribute
  const handleAddValue = () => {
    if (!activeAttributeId) {
      setError("No attribute selected");
      return;
    }

    if (!newValue.trim()) {
      setError("Value is required");
      return;
    }

    // Create a new value locally with a temporary ID
    // The actual ID will be assigned by the backend when the grouped product is created
    const tempId = Date.now(); // Use timestamp as temporary ID
    const newAttributeValue: AttributeValue = {
      id: tempId,
      value: newValue,
      productAttributeId: activeAttributeId
    };

    // Update the attribute values
    const currentValues = attributeValues[activeAttributeId] || [];
    onAttributeValuesChange(activeAttributeId, [...currentValues, newAttributeValue]);

    // Reset form
    setNewValue("");
    setError(null);
  };

  // Remove a value
  const handleRemoveValue = (attributeId: number, valueId: number) => {
    const currentValues = attributeValues[attributeId] || [];
    const updatedValues = currentValues.filter(value => value.id !== valueId);
    onAttributeValuesChange(attributeId, updatedValues);
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium border-b pb-2">Define Attribute Values</h3>

      {error && (
        <div className="p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <FiAlertTriangle className="mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Attribute tabs */}
        <div className="md:col-span-1 space-y-2">
          <h4 className="font-medium mb-3">Attributes</h4>
          <div className="flex flex-col space-y-2">
            {attributes.map(attribute => (
              <button
                key={attribute.id}
                type="button"
                onClick={() => setActiveAttributeId(attribute.id!)}
                className={`text-left px-4 py-2 rounded-md transition-colors ${
                  activeAttributeId === attribute.id
                    ? "bg-main-color text-white"
                    : "bg-gray-100 hover:bg-gray-200"
                }`}
              >
                {attribute.name}
                <span className="ml-2 text-xs">
                  ({attributeValues[attribute.id!]?.length || 0} values)
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Values panel */}
        <div className="md:col-span-3">
          {activeAttributeId ? (
            <>
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium">
                  Values for {attributes.find(a => a.id === activeAttributeId)?.name}
                </h4>
              </div>

              {/* Add new value form */}
              <div className="mb-6 p-4 border border-dashed border-gray-300 rounded-md">
                <div className="flex flex-col md:flex-row gap-3">
                  <input
                    type="text"
                    value={newValue}
                    onChange={e => setNewValue(e.target.value)}
                    placeholder="Value (e.g., Small, Red, Cotton)"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
                  />
                  <button
                    type="button"
                    onClick={handleAddValue}
                    className="px-4 py-2 bg-main-color text-white rounded-md flex items-center justify-center"
                  >
                    <FiPlus className="mr-1" /> Add Value
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Enter the attribute value name (e.g., Small, Red, Cotton)
                </p>
              </div>

              {/* Values list */}
              {(attributeValues[activeAttributeId]?.length || 0) === 0 ? (
                <div className="py-4 text-center text-gray-500">
                  No values defined for this attribute yet. Add your first value above.
                </div>
              ) : (
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Value
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {(attributeValues[activeAttributeId] || []).map(value => (
                        <tr key={value.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {value.value}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right">
                            <button
                              type="button"
                              onClick={() => handleRemoveValue(activeAttributeId, value.id!)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <FiTrash2 />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </>
          ) : (
            <div className="py-8 text-center text-gray-500">
              Select an attribute to manage its values
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AttributeValuesStep;
