import { create } from "zustand";
import { authApi, User, LoginRequest, RegisterRequest, CreateAdminRequest } from "@/services/authApi";

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  register: (email: string, password: string, name?: string) => Promise<User | null>;
  login: (email: string, password: string) => Promise<User | null>;
  logout: () => Promise<void>;
  getProfile: () => Promise<User | null>;
  createAdmin: (adminData: CreateAdminRequest) => Promise<User | null>;
  clearError: () => void;
  isAuthenticated: () => boolean;
}

// Helper to get token from localStorage
const getStoredToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('accessToken');
};

// Helper to remove token from localStorage
const removeToken = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('accessToken');
};

// Helper to clear auth state when token expires
const clearAuthState = (set: any) => {
  removeToken();
  set({
    user: null,
    token: null,
    isLoading: false,
    error: 'Session expired. Please login again.'
  });
};

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  token: getStoredToken(),
  isLoading: false,
  error: null,

  clearError: () => set({ error: null }),

  isAuthenticated: () => {
    return !!get().token;
  },

  register: async (email: string, password: string, name?: string) => {
    set({ isLoading: true, error: null });
    try {
      const registerData: RegisterRequest = { email, password };
      if (name) registerData.name = name;

      // Use the public registration endpoint
      const data = await authApi.registerUser(registerData);

      set({
        user: data,
        token: data.accessToken || null,
        isLoading: false
      });

      return data;
    } catch (error) {
      console.error('Auth store error - register:', error);
      set({
        error: error instanceof Error ? error.message : 'Registration failed',
        isLoading: false,
      });
      return null;
    }
  },

  login: async (email: string, password: string) => {
    set({ isLoading: true, error: null });
    try {
      const data = await authApi.login({ email, password });

      set({
        user: data,
        token: data.accessToken || null,
        isLoading: false
      });

      return data;
    } catch (error) {
      console.error('Auth store error - login:', error);
      set({
        error: error instanceof Error ? error.message : 'Login failed',
        isLoading: false,
      });
      return null;
    }
  },

  logout: async () => {
    set({ isLoading: true });
    try {
      await authApi.logout();

      // Clear token and user data
      removeToken();
      set({ user: null, token: null, isLoading: false, error: null });
    } catch (error) {
      console.error('Auth store error - logout:', error);
      // Still clear the token and user data on error
      removeToken();
      set({ user: null, token: null, isLoading: false, error: null });
    }
  },

  getProfile: async () => {
    if (!getStoredToken()) return null;

    set({ isLoading: true, error: null });
    try {
      const data = await authApi.getProfile();

      set({
        user: data,
        isLoading: false
      });

      return data;
    } catch (error: any) {
      console.error('Auth store error - getProfile:', error);

      // Check if error is due to token expiration (401 status)
      if (error?.response?.status === 401 || error?.status === 401 ||
          (error instanceof Error && error.message.includes('401')) ||
          (error instanceof Error && error.message.includes('Unauthorized'))) {
        console.log('Token expired, clearing auth state');
        clearAuthState(set);
        return null;
      }

      set({
        error: error instanceof Error ? error.message : 'Failed to fetch profile',
        isLoading: false,
      });
      return null;
    }
  },

  createAdmin: async (adminData: CreateAdminRequest) => {
    set({ isLoading: true, error: null });
    try {
      const data = await authApi.createAdmin(adminData);

      set({ isLoading: false });

      return data;
    } catch (error) {
      console.error('Auth store error - createAdmin:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create admin',
        isLoading: false,
      });
      return null;
    }
  },
}));
