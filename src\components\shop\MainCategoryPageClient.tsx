"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { FiGrid, FiList, FiChevronRight, FiChevronLeft, FiShoppingBag } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import { createShopUrl, ensureValidSlug } from "@/utils/slugUtils";
import ProductFilters, { FilterOptions, FilterMetadata } from "./ProductFilters";
import { buildFilterQuery, parseFiltersFromUrl, fetchFilterMetadata, updateUrlWithFilters } from "@/utils/filterUtils";

// Types
interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: string[];
}

interface MainCategoryData {
  mainCategory?: {
    id: number;
    name: string;
    slug: string;
    imageUrl: string;
  };
  categories: Category[];
  data: Product[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
  };
}

interface MainCategoryPageClientProps {
  initialData: MainCategoryData;
  mainCategorySlug: string;
}

const MainCategoryPageClient = ({ initialData, mainCategorySlug }: MainCategoryPageClientProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(initialData);
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [filterMetadata, setFilterMetadata] = useState<FilterMetadata | undefined>();

  const { addItem } = useCartStore();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current pagination state from URL
  const currentPage = parseInt(searchParams?.get('page') || '1');
  const currentLimit = parseInt(searchParams?.get('limit') || '12');

  // Handle filtering and pagination
  const fetchProducts = useCallback(async (newFilters: FilterOptions, page: number, limit: number) => {
    setIsLoading(true);

    try {
      const queryString = buildFilterQuery(newFilters, page, limit);
      const response = await fetch(`/api/shop/${mainCategorySlug}?${queryString}`);

      if (response.ok) {
        const newData = await response.json();
        setData(newData);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  }, [mainCategorySlug]);

  // Initialize filters from URL on component mount
  useEffect(() => {
    const urlFilters = parseFiltersFromUrl(searchParams || new URLSearchParams());
    setFilters(urlFilters);

    // Fetch filter metadata
    fetchFilterMetadata(`/api/shop/${mainCategorySlug}`).then(metadata => {
      if (metadata) {
        setFilterMetadata(metadata);
      }
    });

    // If there are filters in URL, fetch filtered products
    if (Object.keys(urlFilters).length > 0) {
      fetchProducts(urlFilters, currentPage, currentLimit);
    }
  }, [searchParams, mainCategorySlug, currentPage, currentLimit, fetchProducts]);

  // Products per page options
  const limitOptions = [12, 24, 36, 48];

  const { mainCategory, categories, data: products, pagination } = data;

  // Extract main category name from slug since it's not always in the response
  const mainCategoryName = mainCategory?.name || mainCategorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  const handleFiltersChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    updateUrlWithFilters(router, `/shop/${mainCategorySlug}`, filters, 1, currentLimit);
    fetchProducts(filters, 1, currentLimit);
  };

  const handlePageChange = async (newPage: number) => {
    if (newPage === currentPage || isLoading) return;

    updateUrlWithFilters(router, `/shop/${mainCategorySlug}`, filters, newPage, currentLimit);
    fetchProducts(filters, newPage, currentLimit);
  };

  const handleLimitChange = async (newLimit: number) => {
    if (newLimit === currentLimit || isLoading) return;

    updateUrlWithFilters(router, `/shop/${mainCategorySlug}`, filters, 1, newLimit);
    fetchProducts(filters, 1, newLimit);
  };

  const handleAddToCart = (product: Product) => {
    setAddingToCartId(product.id);

    try {
      addItem({
        id: product.id.toString(),
        name: product.name,
        price: product.salePrice || product.price,
        image: product.imageUrl,
        sku: product.sku || `PROD-${product.id}`,
        stockQuantity: 100, // Default value
        stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK'
      }, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setTimeout(() => setAddingToCartId(null), 500); // Small delay for better UX
    }
  };

  // Calculate pagination info
  const totalPages = Math.ceil((pagination?.total || 0) / (pagination?.limit || 12));
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      {/* <div 
        className="relative h-64 md:h-80 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('${mainCategory?.imageUrl || 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80'}')`
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{mainCategory?.name || 'Category'}</h1>
            <p className="text-lg md:text-xl max-w-2xl mx-auto">
              Discover our premium collection of {mainCategory?.name?.toLowerCase() || 'beauty'} products
            </p>
          </div>
        </div>
      </div> */}

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-main-color">
            Home
          </Link>
          <FiChevronRight className="mx-2" />
          <Link href="/shop" className="hover:text-main-color">
            Shop
          </Link>
          <FiChevronRight className="mx-2" />
          <span className="font-medium text-gray-800">{mainCategoryName}</span>
        </nav>

        {/* Categories Grid */}
        {categories && categories.length > 0 && (
          <section className="mb-16">

            {/* Responsive grid that scales well for up to 15+ categories */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
              {categories.map((category) => (
                <Link
                  key={category.id}
                  href={createShopUrl(ensureValidSlug(mainCategorySlug), ensureValidSlug(category.slug))}
                  className="group flex flex-col items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1"
                >
                  {/* Circular Image Container */}
                  <div className="relative w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 mb-4 overflow-hidden rounded-full ring-2 ring-gray-100 group-hover:ring-main-color/30 transition-all duration-300">
                    <Image
                      src={category.imageUrl}
                      alt={category.name}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                      sizes="(max-width: 640px) 80px, (max-width: 768px) 96px, 112px"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>

                  {/* Category Info */}
                  <div className="text-center">
                    <h3 className="font-semibold text-gray-900 text-sm sm:text-base mb-1 line-clamp-2 group-hover:text-main-color transition-colors duration-300">
                      {category.name}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-500">
                      {category.count} {category.count === 1 ? 'product' : 'products'}
                    </p>
                  </div>

                  {/* Hover indicator */}
                  <div className="mt-2 w-0 h-0.5 bg-main-color group-hover:w-8 transition-all duration-300 rounded-full"></div>
                </Link>
              ))}
            </div>
          </section>
        )}

        {/* Products Section */}
        {products && products.length > 0 && (
          <section>
            {/* Section Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
              <div className="mb-6 lg:mb-0">
                <h2 className="text-3xl font-bold text-gray-800 mb-2">
                  {mainCategoryName} Products
                </h2>
                <p className="text-gray-600">
                  Showing {products?.length || 0} of {pagination?.total || 0} products
                </p>
              </div>

              {/* Controls */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                {/* Products per page selector */}
                <div className="flex items-center gap-2">
                  <label htmlFor="limit" className="text-sm font-medium text-gray-700 whitespace-nowrap">
                    Show:
                  </label>
                  <select
                    id="limit"
                    value={currentLimit}
                    onChange={(e) => handleLimitChange(parseInt(e.target.value))}
                    disabled={isLoading}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent disabled:opacity-50"
                  >
                    {limitOptions.map((option) => (
                      <option key={option} value={option}>
                        {option} products
                      </option>
                    ))}
                  </select>
                </div>

                {/* View mode toggle */}
                <div className="flex items-center border border-gray-300 rounded-md overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-main-color text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-50'
                    }`}
                    title="Grid view"
                  >
                    <FiGrid size={16} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 transition-colors ${
                      viewMode === 'list'
                        ? 'bg-main-color text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-50'
                    }`}
                    title="List view"
                  >
                    <FiList size={16} />
                  </button>
                </div>

              </div>
            </div>

            {/* Filters and Products Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Filters Sidebar */}
              <div className="lg:col-span-1">
                <ProductFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onApplyFilters={handleApplyFilters}
                  isLoading={isLoading}
                  metadata={filterMetadata}
                  showCategoryFilter={false}
                />
              </div>

              {/* Products Content */}
              <div className="lg:col-span-3">
                {/* Loading Overlay */}
            {isLoading && (
              <div className="relative">
                <div className="absolute inset-0 bg-white/70 backdrop-blur-sm z-10 flex items-center justify-center">
                  <div className="flex items-center gap-3 bg-white px-6 py-3 rounded-lg shadow-lg">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-main-color"></div>
                    <span className="text-gray-700 font-medium">Loading products...</span>
                  </div>
                </div>
              </div>
            )}

            {/* Products Display */}
            <div className={`transition-opacity duration-300 ${isLoading ? 'opacity-50' : 'opacity-100'}`}>
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {products.map((product) => (
                    <div key={product.id} className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
                      <div className="relative h-64 overflow-hidden rounded-t-lg">
                        <Link href={`/product/${ensureValidSlug(product.slug)}`}>
                          <Image
                            src={product.imageUrl}
                            alt={product.name}
                            fill
                            className="object-cover transition-transform duration-300 group-hover:scale-105"
                          />
                        </Link>
                        {product.salePrice && (
                          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                            Sale
                          </div>
                        )}
                        {!product.inStock && (
                          <div className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 rounded text-sm font-medium">
                            Out of Stock
                          </div>
                        )}
                      </div>

                      <div className="p-4">
                        <Link href={`/product/${ensureValidSlug(product.slug)}`}>
                          <h3 className="font-medium text-gray-800 mb-2 line-clamp-2 hover:text-main-color transition-colors">
                            {product.name}
                          </h3>
                        </Link>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {product.salePrice ? (
                              <>
                                <span className="text-lg font-bold text-main-color">
                                  ${product.salePrice.toFixed(2)}
                                </span>
                                <span className="text-sm text-gray-500 line-through">
                                  ${product.price.toFixed(2)}
                                </span>
                              </>
                            ) : (
                              <span className="text-lg font-bold text-gray-800">
                                ${product.price.toFixed(2)}
                              </span>
                            )}
                          </div>

                          <button
                            onClick={() => handleAddToCart(product)}
                            disabled={!product.inStock || addingToCartId === product.id}
                            className={`p-2 rounded-full transition-colors ${
                              product.inStock
                                ? "bg-main-color text-white hover:bg-main-color/90"
                                : "bg-gray-300 text-gray-500 cursor-not-allowed"
                            }`}
                          >
                            {addingToCartId === product.id ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <FiShoppingBag size={16} />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {products.map((product) => (
                    <div key={product.id} className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 p-4">
                      <div className="flex gap-4">
                        <div className="relative w-24 h-24 flex-shrink-0 overflow-hidden rounded-lg">
                          <Link href={`/product/${ensureValidSlug(product.slug)}`}>
                            <Image
                              src={product.imageUrl}
                              alt={product.name}
                              fill
                              className="object-cover transition-transform duration-300 group-hover:scale-105"
                            />
                          </Link>
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0 mr-4">
                              <Link href={`/product/${ensureValidSlug(product.slug)}`}>
                                <h3 className="font-medium text-gray-800 mb-1 hover:text-main-color transition-colors">
                                  {product.name}
                                </h3>
                              </Link>

                              {product.shortDescription && (
                                <div
                                  className="text-sm text-gray-600 mb-2 max-h-12 overflow-hidden leading-tight"
                                  dangerouslySetInnerHTML={{ __html: product.shortDescription }}
                                />
                              )}

                              <div className="flex items-center gap-2 mb-2">
                                {product.salePrice ? (
                                  <>
                                    <span className="text-lg font-bold text-main-color">
                                      ${product.salePrice.toFixed(2)}
                                    </span>
                                    <span className="text-sm text-gray-500 line-through">
                                      ${product.price.toFixed(2)}
                                    </span>
                                    <span className="bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                                      Sale
                                    </span>
                                  </>
                                ) : (
                                  <span className="text-lg font-bold text-gray-800">
                                    ${product.price.toFixed(2)}
                                  </span>
                                )}
                              </div>

                              <div className="flex items-center gap-2">
                                <span className={`px-2 py-1 rounded text-xs font-medium ${
                                  product.inStock
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {product.inStock ? 'In Stock' : 'Out of Stock'}
                                </span>
                              </div>
                            </div>

                            <button
                              onClick={() => handleAddToCart(product)}
                              disabled={!product.inStock || addingToCartId === product.id}
                              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                product.inStock
                                  ? "bg-main-color text-white hover:bg-main-color/90"
                                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
                              }`}
                            >
                              {addingToCartId === product.id ? (
                                <div className="flex items-center gap-2">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                  Adding...
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  <FiShoppingBag size={16} />
                                  Add to Cart
                                </div>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-12 flex flex-col sm:flex-row items-center justify-between gap-4">
                {/* Pagination Info */}
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * (pagination?.limit || 12)) + 1} to{' '}
                  {Math.min(currentPage * (pagination?.limit || 12), pagination?.total || 0)} of{' '}
                  {pagination?.total || 0} products
                </div>

                {/* Pagination Controls */}
                <div className="flex items-center gap-2">
                  {/* Previous Button */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!hasPrevPage || isLoading}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                      hasPrevPage && !isLoading
                        ? 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        : 'border-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <FiChevronLeft size={16} />
                    <span className="hidden sm:inline">Previous</span>
                  </button>

                  {/* Page Numbers */}
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                      let pageNumber;

                      if (totalPages <= 7) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 4) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 3) {
                        pageNumber = totalPages - 6 + i;
                      } else {
                        pageNumber = currentPage - 3 + i;
                      }

                      if (pageNumber < 1 || pageNumber > totalPages) return null;

                      return (
                        <button
                          key={pageNumber}
                          onClick={() => handlePageChange(pageNumber)}
                          disabled={isLoading}
                          className={`w-10 h-10 rounded-lg border transition-colors ${
                            pageNumber === currentPage
                              ? 'border-main-color bg-main-color text-white'
                              : isLoading
                              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}

                    {/* Show ellipsis and last page if needed */}
                    {totalPages > 7 && currentPage < totalPages - 3 && (
                      <>
                        <span className="px-2 text-gray-400">...</span>
                        <button
                          onClick={() => handlePageChange(totalPages)}
                          disabled={isLoading}
                          className={`w-10 h-10 rounded-lg border transition-colors ${
                            isLoading
                              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {totalPages}
                        </button>
                      </>
                    )}
                  </div>

                  {/* Next Button */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!hasNextPage || isLoading}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                      hasNextPage && !isLoading
                        ? 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        : 'border-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <span className="hidden sm:inline">Next</span>
                    <FiChevronRight size={16} />
                  </button>
                </div>
              </div>
            )}
              </div>
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default MainCategoryPageClient;
