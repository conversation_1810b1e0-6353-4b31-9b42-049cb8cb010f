// Authentication API service

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  // Log the response status and URL for debugging
  console.log(`API Response: ${response.status} ${response.url}`);

  if (!response.ok) {
    // Try to get error details from the response
    let errorMessage = `Error: ${response.status} ${response.statusText}`;
    let errorDetails = '';

    try {
      // Only try to parse JSON if the content type is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json();
        console.error('API Error Response (JSON):', errorData);
        errorMessage = errorData.message || errorMessage;
        errorDetails = JSON.stringify(errorData, null, 2);
      } else {
        // For non-JSON responses, try to get the text
        const text = await response.text();
        console.error('API Error Response (Text):', text);
        if (text) {
          errorMessage = `${errorMessage} - ${text}`;
          errorDetails = text;
        }
      }
    } catch (e) {
      console.error('Error parsing error response:', e);
      // If we can't parse the error response, use the default message
    }

    console.error(`API Error: ${errorMessage}`);
    if (errorDetails) {
      console.error(`API Error Details: ${errorDetails}`);
    }

    throw new Error(errorMessage);
  }

  try {
    const jsonData = await response.json();
    console.log('Parsed JSON response:', jsonData);
    return jsonData;
  } catch (e) {
    console.error('Error parsing JSON response:', e);
    return {} as T;
  }
};

// User interface
export interface User {
  id: number;
  email: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  accessToken?: string;
}

// Login request interface
export interface LoginRequest {
  email: string;
  password: string;
}

// Register request interface
export interface RegisterRequest {
  email: string;
  password: string;
  name?: string;
  firstName?: string;
  lastName?: string;
}

// Create admin request interface
export interface CreateAdminRequest {
  email: string;
  password: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  isSuperAdmin: boolean;
}

// Auth API service
export const authApi = {
  // Register a new user (public endpoint, no authentication required)
  registerUser: async (userData: RegisterRequest): Promise<User> => {
    try {
      console.log('Registering new user:', userData.email);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/auth/register-user';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await handleResponse<User>(response);

      // Store the access token in localStorage
      if (data.accessToken) {
        localStorage.setItem('accessToken', data.accessToken);
      }

      return data;
    } catch (error) {
      console.error('Failed to register user:', error);
      throw error;
    }
  },

  // Login
  login: async (credentials: LoginRequest): Promise<User> => {
    try {
      console.log('Logging in...');

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/auth/login';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      const data = await handleResponse<User>(response);

      // Store the access token in localStorage
      if (data.accessToken) {
        localStorage.setItem('accessToken', data.accessToken);
      }

      return data;
    } catch (error) {
      console.error('Failed to login:', error);
      throw error;
    }
  },

  // Get user profile
  getProfile: async (): Promise<User> => {
    try {
      console.log('Fetching user profile...');

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/auth/profile';

      const response = await fetch(proxyUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });
      return await handleResponse<User>(response);
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      throw error;
    }
  },

  // Create admin
  createAdmin: async (adminData: CreateAdminRequest): Promise<User> => {
    try {
      console.log('Creating admin user:', adminData);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/auth/admin';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
        body: JSON.stringify(adminData),
      });
      return await handleResponse<User>(response);
    } catch (error) {
      console.error('Failed to create admin user:', error);
      throw error;
    }
  },

  // Logout
  logout: async (): Promise<{ message: string }> => {
    try {
      console.log('Logging out...');

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/auth/logout';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
        },
      });

      // Remove the access token from localStorage
      localStorage.removeItem('accessToken');

      return await handleResponse<{ message: string }>(response);
    } catch (error) {
      console.error('Failed to logout:', error);
      // Remove the access token from localStorage even if the API call fails
      localStorage.removeItem('accessToken');
      throw error;
    }
  },
};

export default authApi;
