"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { FiGrid, FiList, FiChevronRight, FiChevronLeft, FiShoppingBag, FiPackage, FiTruck, FiDollarSign } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import ProductFilters, { FilterOptions, FilterMetadata } from "./ProductFilters";
import { buildFilterQuery, parseFiltersFromUrl, fetchFilterMetadata, updateUrlWithFilters } from "@/utils/filterUtils";
import ProductCard from "@/components/shop/ProductCard";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  stockQuantity: number | null;
  stockStatus: string;
  productType: string;
  discountPercent?: number;
  isOnSale: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  categories?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  access: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  imageUrl: string;
}

interface WholesaleData {
  data: Product[];
  pagination: {
    total: number;
    page: number | string;
    limit: number | string;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  category?: Category;
  appliedFilters: any;
}

interface WholesalePageClientProps {
  initialData: WholesaleData;
}

const WholesalePageClient = ({ initialData }: WholesalePageClientProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(initialData);
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [filterMetadata, setFilterMetadata] = useState<FilterMetadata | undefined>();

  const { addItem } = useCartStore();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current pagination state from URL
  const currentPage = parseInt(searchParams?.get('page') || '1');
  const currentLimit = parseInt(searchParams?.get('limit') || '12');

  // Handle filtering and pagination
  const fetchProducts = useCallback(async (newFilters: FilterOptions, page: number, limit: number) => {
    setIsLoading(true);

    try {
      const queryString = buildFilterQuery(newFilters, page, limit);
      const response = await fetch(`/api/wholesale?${queryString}`);

      if (response.ok) {
        const newData = await response.json();
        setData(newData);
      }
    } catch (error) {
      console.error('Error fetching wholesale products:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize filters from URL on component mount
  useEffect(() => {
    const urlFilters = parseFiltersFromUrl(searchParams || new URLSearchParams());
    setFilters(urlFilters);

    // Fetch filter metadata
    fetchFilterMetadata('/api/wholesale').then(metadata => {
      if (metadata) {
        setFilterMetadata(metadata);
      }
    });

    // If there are filters in URL, fetch filtered products
    if (Object.keys(urlFilters).length > 0) {
      fetchProducts(urlFilters, currentPage, currentLimit);
    } else if (!data.data || data.data.length === 0) {
      // If no initial products (fallback data), fetch real data
      fetchProducts({}, currentPage, currentLimit);
    }
  }, [searchParams, currentPage, currentLimit, fetchProducts, data.data]);

  // Products per page options
  const limitOptions = [12, 24, 36, 48];

  const { category, data: products, pagination } = data;

  const handleFiltersChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    updateUrlWithFilters(router, '/wholesale', filters, 1, currentLimit);
    fetchProducts(filters, 1, currentLimit);
  };

  const handlePageChange = (newPage: number) => {
    updateUrlWithFilters(router, '/wholesale', filters, newPage, currentLimit);
    fetchProducts(filters, newPage, currentLimit);
  };

  const handleLimitChange = (newLimit: number) => {
    updateUrlWithFilters(router, '/wholesale', filters, 1, newLimit);
    fetchProducts(filters, 1, newLimit);
  };

  const handleAddToCart = async (product: Product) => {
    setAddingToCartId(product.id);
    try {
      await addItem({
        id: product.id.toString(),
        name: product.name,
        image: product.imageUrl,
        price: product.salePrice || product.price,
        stockQuantity: product.stockQuantity,
        sku: product.sku,
        stockStatus: product.stockStatus || (product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK')
      }, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingToCartId(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const getStockWarning = (stockQuantity: number | null) => {
    if (stockQuantity === null) return null;
    if (stockQuantity <= 3 && stockQuantity > 0) {
      return `Only ${stockQuantity} left in stock!`;
    }
    return null;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section with Wholesale Branding */}
      <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16 md:py-24">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-white/10 rounded-full backdrop-blur-sm">
                <FiPackage size={48} className="text-white" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              Wholesale Solutions
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed">
              {category?.description || "Premium bulk cosmetics and beauty products for businesses. Competitive pricing, reliable supply, and exceptional quality."}
            </p>
            
            {/* Wholesale Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
              <div className="flex items-center justify-center gap-3 p-4 bg-white/10 rounded-lg backdrop-blur-sm">
                <FiDollarSign size={24} />
                <span className="font-semibold">Bulk Pricing</span>
              </div>
              <div className="flex items-center justify-center gap-3 p-4 bg-white/10 rounded-lg backdrop-blur-sm">
                <FiTruck size={24} />
                <span className="font-semibold">Fast Shipping</span>
              </div>
              <div className="flex items-center justify-center gap-3 p-4 bg-white/10 rounded-lg backdrop-blur-sm">
                <FiPackage size={24} />
                <span className="font-semibold">Quality Assured</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-main-color">
            Home
          </Link>
          <FiChevronRight className="mx-2" />
          <span className="font-medium text-gray-800">Wholesale</span>
        </nav>

        {/* Filters and Products Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <ProductFilters
              filters={filters}
              onFiltersChange={handleFiltersChange}
              onApplyFilters={handleApplyFilters}
              isLoading={isLoading}
              metadata={filterMetadata}
              showCategoryFilter={false}
            />
          </div>

          {/* Products Section */}
          <div className="lg:col-span-3">
            {/* Products Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
              <div className="flex items-center gap-4">
                <h2 className="text-2xl font-bold text-gray-900">
                  Wholesale Products
                  {pagination && (
                    <span className="text-lg font-normal text-gray-600 ml-2">
                      ({pagination.total} products)
                    </span>
                  )}
                </h2>
              </div>

              <div className="flex items-center gap-4">
                {/* Products per page */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Show:</span>
                  <select
                    value={currentLimit}
                    onChange={(e) => handleLimitChange(Number(e.target.value))}
                    className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent"
                  >
                    {limitOptions.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                </div>

                {/* View Mode Toggle */}
                <div className="flex items-center border border-gray-300 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 ${viewMode === "grid" ? "bg-main-color text-white" : "bg-white text-gray-600 hover:bg-gray-50"}`}
                  >
                    <FiGrid size={16} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 ${viewMode === "list" ? "bg-main-color text-white" : "bg-white text-gray-600 hover:bg-gray-50"}`}
                  >
                    <FiList size={16} />
                  </button>
                </div>
              </div>
            </div>

            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
              </div>
            )}

            {/* Products Grid/List */}
            {!isLoading && products && products.length > 0 && (
              <div className={viewMode === "grid" 
                ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" 
                : "space-y-6"
              }>
                {products.map((product) => (
                  <div
                    key={product.id}
                    className={`group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100 ${
                      viewMode === "list" ? "flex gap-6 p-6" : "flex flex-col"
                    }`}
                  >
                    {/* Product Image */}
                    <div className={`relative overflow-hidden ${
                      viewMode === "list" ? "w-48 h-48 flex-shrink-0" : "h-64"
                    }`}>
                      <Link href={`/product/${product.slug}`}>
                        <Image
                          src={product.imageUrl}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </Link>
                      {product.isOnSale && product.discountPercent && (
                        <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                          -{product.discountPercent}%
                        </div>
                      )}
                      {!product.inStock && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                          <span className="text-white font-semibold">Out of Stock</span>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className={`${viewMode === "list" ? "flex-1" : "p-4"}`}>
                      <Link href={`/product/${product.slug}`}>
                        <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-main-color transition-colors line-clamp-2">
                          {product.name}
                        </h3>
                      </Link>

                      {/* Short Description (List view only) */}
                      {viewMode === "list" && product.shortDescription && (
                        <div
                          className="text-sm text-gray-600 mb-3 line-clamp-3"
                          dangerouslySetInnerHTML={{ __html: product.shortDescription }}
                        />
                      )}

                      {/* Price */}
                      <div className="flex items-center gap-2 mb-3">
                        {product.salePrice ? (
                          <>
                            <span className="text-lg font-bold text-main-color">
                              {formatPrice(product.salePrice)}
                            </span>
                            <span className="text-sm text-gray-500 line-through">
                              {formatPrice(product.price)}
                            </span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-gray-900">
                            {formatPrice(product.price)}
                          </span>
                        )}
                      </div>

                      {/* Stock Warning */}
                      {getStockWarning(product.stockQuantity) && (
                        <p className="text-sm text-orange-600 font-medium mb-3">
                          {getStockWarning(product.stockQuantity)}
                        </p>
                      )}

                      {/* Add to Cart Button */}
                      <button
                        onClick={() => handleAddToCart(product)}
                        disabled={!product.inStock || addingToCartId === product.id}
                        className={`w-full py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 ${
                          product.inStock
                            ? "bg-main-color text-white hover:bg-main-color/90 hover:shadow-md"
                            : "bg-gray-200 text-gray-500 cursor-not-allowed"
                        }`}
                      >
                        {addingToCartId === product.id ? (
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        ) : (
                          <>
                            <FiShoppingBag size={16} />
                            {product.inStock ? "Add to Cart" : "Out of Stock"}
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* No Products Found */}
            {!isLoading && (!products || products.length === 0) && (
              <div className="text-center py-12">
                <div className="mb-4">
                  <FiPackage size={48} className="mx-auto text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No wholesale products found</h3>
                <p className="text-gray-600 mb-6">Try adjusting your filters or check back later for new products.</p>
                <button
                  onClick={() => {
                    setFilters({});
                    updateUrlWithFilters(router, '/wholesale', {}, 1, currentLimit);
                    fetchProducts({}, 1, currentLimit);
                  }}
                  className="px-6 py-2 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            )}

            {/* Pagination */}
            {!isLoading && products && products.length > 0 && pagination && pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
                <div className="text-sm text-gray-600">
                  Showing {((Number(pagination.page) - 1) * Number(pagination.limit)) + 1} to{' '}
                  {Math.min(Number(pagination.page) * Number(pagination.limit), pagination.total)} of{' '}
                  {pagination.total} products
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handlePageChange(Number(pagination.page) - 1)}
                    disabled={!pagination.hasPrev}
                    className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <FiChevronLeft size={16} />
                    Previous
                  </button>

                  <span className="px-4 py-2 text-sm font-medium text-gray-700">
                    Page {pagination.page} of {pagination.totalPages}
                  </span>

                  <button
                    onClick={() => handlePageChange(Number(pagination.page) + 1)}
                    disabled={!pagination.hasNext}
                    className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Next
                    <FiChevronRight size={16} />
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default WholesalePageClient;
