import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_AUTH_PATH_PREFIX = '/api/auth';

// Log the API configuration to help with debugging
console.log('Auth API - Base URL:', API_BASE_URL);
console.log('Auth API - Path Prefix:', API_AUTH_PATH_PREFIX);

// POST handler for registering a new user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log(`API proxy - Registering user with email: ${body.email}`);

    const response = await fetch(`${API_BASE_URL}${API_AUTH_PATH_PREFIX}/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    // Return the response with the same status code
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /auth/register):', error);
    return NextResponse.json(
      { error: 'Failed to register user' },
      { status: 500 }
    );
  }
}
