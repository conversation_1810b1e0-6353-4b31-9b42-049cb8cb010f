// Hierarchical categories data model
export interface SubCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
  parentId: number;
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
  subcategories: SubCategory[];
}

// API endpoints - Use Next.js API routes instead of direct external API calls
// This ensures proper CORS handling and consistent API behavior

// Fetch all main categories
export const fetchMainCategories = async (): Promise<Category[]> => {
  try {
    const response = await fetch('/api/store/main-categories');

    if (!response.ok) {
      throw new Error(`Failed to fetch main categories: ${response.status}`);
    }

    const data = await response.json();
    console.log('Main categories API response:', data);

    // Transform the API response to match our Category interface
    // The API returns an array directly, not wrapped in a data property
    return data.map((item: any) => ({
      id: item.id,
      name: item.name,
      slug: item.slug,
      imageUrl: item.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image',
      count: item.count || 0,
      subcategories: [] // Initially empty, will be populated when needed
    }));
  } catch (error) {
    console.error('Error fetching main categories:', error);
    return [];
  }
};

// Fetch subcategories for a specific main category
export const fetchSubcategories = async (mainCategoryId: number): Promise<SubCategory[]> => {
  try {
    // Get all categories
    const response = await fetch('/api/store/categories');

    if (!response.ok) {
      throw new Error(`Failed to fetch subcategories: ${response.status}`);
    }

    const data = await response.json();
    console.log('Categories API response:', data);

    // Filter categories by mainCategoryId
    const subcategories = data.filter((item: any) => item.mainCategoryId === mainCategoryId);

    // Transform the API response to match our SubCategory interface
    return subcategories.map((item: any) => ({
      id: item.id,
      name: item.name,
      slug: item.slug,
      imageUrl: item.imageUrl || 'https://via.placeholder.com/800x600?text=No+Image',
      count: item.count || 0,
      parentId: mainCategoryId
    }));
  } catch (error) {
    console.error(`Error fetching subcategories for main category ${mainCategoryId}:`, error);
    return [];
  }
};

// Fetch products for a specific category
export const fetchProductsByCategory = async (categoryId: number, page: number = 1, limit: number = 20) => {
  try {
    const response = await fetch(`/api/store/categories/${categoryId}/products`);

    if (!response.ok) {
      throw new Error(`Failed to fetch products: ${response.status}`);
    }

    const data = await response.json();
    console.log('Products API response:', data);

    // The API doesn't support pagination yet, so we'll implement it client-side
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = data.slice(startIndex, endIndex);

    // Transform the data to match the expected format
    return {
      pagination: {
        total: data.length,
        page: page,
        limit: limit
      },
      data: paginatedData.map((product: any) => {
        // Get the date from a week ago
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        // Parse the creation date or use current date if not available
        const creationDate = product.createdAt ? new Date(product.createdAt) : new Date();

        // Check if the product is new (created in the last week)
        const isNew = creationDate > oneWeekAgo;

        // Check if the product is on sale (has a sale price lower than regular price)
        // For demo purposes, we'll randomly set some products on sale
        const hasSalePrice = Math.random() > 0.7; // 30% chance of being on sale
        const salePrice = hasSalePrice ? parseFloat((parseFloat(product.price) * 0.8).toFixed(2)) : null;

        return {
          id: product.id,
          sku: product.sku,
          name: product.name,
          slug: product.slug || product.name.toLowerCase().replace(/\s+/g, '-'),
          imageUrl: product.images && product.images.length > 0 ? product.images[0].url : 'https://via.placeholder.com/800x600?text=No+Image',
          price: parseFloat(product.price),
          salePrice: salePrice,
          inStock: product.stockStatus === 'IN_STOCK',
          created_at: product.createdAt || new Date().toISOString(),
          description: product.shortDescription,
          isNew: isNew
        };
      })
    };
  } catch (error) {
    console.error(`Error fetching products for category ${categoryId}:`, error);
    return {
      pagination: { total: 0, page: 1, limit },
      data: []
    };
  }
};

// For backward compatibility with existing code, we'll provide a default set of categories
// This will be used as a fallback if the API calls fail
export const categories: Category[] = [];

// Helper function to get all subcategories as a flat array
export const getAllSubcategories = (): SubCategory[] => {
  return categories.flatMap(category => category.subcategories);
};

// Helper function to find a category by ID
export const findCategoryById = (id: number): Category | undefined => {
  return categories.find(category => category.id === id);
};

// Helper function to find a subcategory by ID
export const findSubcategoryById = (id: number): SubCategory | undefined => {
  return getAllSubcategories().find(subcategory => subcategory.id === id);
};

// Helper function to find a subcategory's parent category
export const findParentCategory = (subcategoryId: number): Category | undefined => {
  const subcategory = findSubcategoryById(subcategoryId);
  if (subcategory) {
    return findCategoryById(subcategory.parentId);
  }
  return undefined;
};

// Helper function to get product count for a category
const getProductCountForCategory = async (categoryId: number): Promise<number> => {
  try {
    const response = await fetch(`/api/store/categories/${categoryId}/products`);
    if (!response.ok) {
      return 0;
    }
    const products = await response.json();
    return Array.isArray(products) ? products.length : 0;
  } catch (error) {
    console.error(`Error fetching product count for category ${categoryId}:`, error);
    return 0;
  }
};

// Load categories and subcategories with accurate product counts
export const loadCategoriesWithSubcategories = async (): Promise<Category[]> => {
  try {
    // First, fetch all main categories
    const mainCategories = await fetchMainCategories();

    // For each main category, fetch its subcategories and calculate counts
    const categoriesWithSubcategories = await Promise.all(
      mainCategories.map(async (category) => {
        const subcategories = await fetchSubcategories(category.id);

        // Get product counts for each subcategory
        const subcategoriesWithCounts = await Promise.all(
          subcategories.map(async (subcategory) => {
            const productCount = await getProductCountForCategory(subcategory.id);
            return {
              ...subcategory,
              count: productCount
            };
          })
        );

        // Calculate total count for main category (sum of all subcategory counts)
        const totalCount = subcategoriesWithCounts.reduce((sum, subcat) => sum + subcat.count, 0);

        return {
          ...category,
          count: totalCount,
          subcategories: subcategoriesWithCounts
        };
      })
    );

    // Update the categories array for backward compatibility
    categories.length = 0;
    categories.push(...categoriesWithSubcategories);

    return categoriesWithSubcategories;
  } catch (error) {
    console.error('Error loading categories with subcategories:', error);
    return [];
  }
};
