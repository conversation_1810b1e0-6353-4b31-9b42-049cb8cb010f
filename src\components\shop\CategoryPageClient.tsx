"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { FiGrid, FiList, FiChevronRight, FiChevronLeft, FiShoppingBag } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import { ensureValidSlug } from "@/utils/slugUtils";
import ProductFilters, { FilterOptions, FilterMetadata } from "./ProductFilters";
import { buildFilterQuery, parseFiltersFromUrl, fetchFilterMetadata, updateUrlWithFilters } from "@/utils/filterUtils";
import ProductCard from "@/components/shop/ProductCard";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  access: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
}

interface CategoryData {
  category?: {
    id: number;
    name: string;
    slug: string;
    imageUrl: string;
    description?: string;
  };
  data: Product[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
  };
}

interface MainCategoryData {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
}

interface CategoryPageClientProps {
  initialData: CategoryData;
  mainCategoryData?: MainCategoryData;
  mainCategorySlug: string;
  categorySlug: string;
}

const CategoryPageClient = ({
  initialData,
  mainCategoryData,
  mainCategorySlug,
  categorySlug
}: CategoryPageClientProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(initialData);
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [filterMetadata, setFilterMetadata] = useState<FilterMetadata | undefined>();

  const { addItem } = useCartStore();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current pagination state from URL
  const currentPage = parseInt(searchParams?.get('page') || '1');
  const currentLimit = parseInt(searchParams?.get('limit') || '12');

  // Handle filtering and pagination
  const fetchProducts = useCallback(async (newFilters: FilterOptions, page: number, limit: number) => {
    setIsLoading(true);

    try {
      const queryString = buildFilterQuery(newFilters, page, limit);
      const response = await fetch(`/api/shop/${mainCategorySlug}/${categorySlug}?${queryString}`);

      if (response.ok) {
        const newData = await response.json();
        setData(newData);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  }, [mainCategorySlug, categorySlug]);

  // Initialize filters from URL on component mount
  useEffect(() => {
    const urlFilters = parseFiltersFromUrl(searchParams || new URLSearchParams());
    setFilters(urlFilters);

    // Fetch filter metadata
    fetchFilterMetadata(`/api/shop/${mainCategorySlug}/${categorySlug}`).then(metadata => {
      if (metadata) {
        setFilterMetadata(metadata);
      }
    });

    // If there are filters in URL, fetch filtered products
    if (Object.keys(urlFilters).length > 0) {
      fetchProducts(urlFilters, currentPage, currentLimit);
    }
  }, [searchParams, mainCategorySlug, categorySlug, currentPage, currentLimit, fetchProducts]);

  // Products per page options
  const limitOptions = [12, 24, 36, 48];

  const { category, data: products, pagination } = data;

  // Extract category names from slugs if not in response
  const mainCategoryName = mainCategoryData?.name || mainCategorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  const categoryName = category?.name || categorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

  const handleFiltersChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  const handleApplyFilters = () => {
    updateUrlWithFilters(router, `/shop/${mainCategorySlug}/${categorySlug}`, filters, 1, currentLimit);
    fetchProducts(filters, 1, currentLimit);
  };

  const handlePageChange = async (newPage: number) => {
    if (newPage === currentPage || isLoading) return;

    updateUrlWithFilters(router, `/shop/${mainCategorySlug}/${categorySlug}`, filters, newPage, currentLimit);
    fetchProducts(filters, newPage, currentLimit);
  };

  const handleLimitChange = async (newLimit: number) => {
    if (newLimit === currentLimit || isLoading) return;

    updateUrlWithFilters(router, `/shop/${mainCategorySlug}/${categorySlug}`, filters, 1, newLimit);
    fetchProducts(filters, 1, newLimit);
  };

  const handleAddToCart = (product: Product) => {
    if (!product.inStock) return;

    setAddingToCartId(product.id);

    try {
      addItem({
        id: product.id.toString(),
        name: product.name,
        price: product.salePrice || product.price,
        image: product.imageUrl,
        sku: product.sku || `PROD-${product.id}`,
        stockQuantity: 100, // Default value
        stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK'
      }, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setTimeout(() => setAddingToCartId(null), 500); // Small delay for better UX
    }
  };

  // Calculate pagination info
  const totalPages = Math.ceil((pagination?.total || 0) / (pagination?.limit || 12));
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Elegant Category Header */}
      <div className="bg-gradient-to-br from-slate-50 via-white to-gray-50 relative overflow-hidden border-b border-gray-100">
        {/* Subtle background pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(12,125,143,0.03)_0%,transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(12,125,143,0.02)_0%,transparent_50%)]"></div>

        <div className="relative px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-16 md:py-20 lg:py-24">
          <div className="max-w-6xl mx-auto">
            <div className="flex flex-col lg:flex-row items-center lg:items-start gap-12 lg:gap-16">
              {/* Category Image - Elegant Circular Design */}
              <div className="flex-shrink-0 lg:order-1">
                <div className="relative group">
                  {/* Main image container */}
                  <div className="relative w-56 h-56 md:w-72 md:h-72 lg:w-80 lg:h-80 mx-auto">
                    {/* Outer decorative ring */}
                    <div className="absolute inset-0 rounded-full bg-gradient-to-br from-main-color/10 via-main-color/5 to-transparent p-3">
                      {/* Inner shadow ring */}
                      <div className="absolute inset-3 rounded-full bg-white shadow-xl"></div>
                      {/* Image container */}
                      <div className="relative w-full h-full rounded-full overflow-hidden shadow-2xl ring-1 ring-gray-200/50">
                        <Image
                          src={category?.imageUrl || 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80'}
                          alt={category?.name || 'Category'}
                          fill
                          className="object-cover transition-all duration-500 group-hover:scale-105"
                          priority
                          sizes="(max-width: 768px) 224px, (max-width: 1024px) 288px, 320px"
                        />
                        {/* Subtle overlay */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent"></div>
                      </div>
                    </div>

                    {/* Floating accent elements */}
                    <div className="absolute -top-2 -right-2 w-4 h-4 bg-main-color/20 rounded-full animate-pulse"></div>
                    <div className="absolute -bottom-3 -left-3 w-6 h-6 bg-main-color/15 rounded-full animate-pulse delay-1000"></div>
                    <div className="absolute top-1/4 -right-4 w-3 h-3 bg-main-color/10 rounded-full animate-pulse delay-500 hidden lg:block"></div>
                  </div>
                </div>
              </div>

              {/* Category Content */}
              <div className="flex-1 text-center lg:text-left lg:order-2 max-w-2xl lg:max-w-none mx-auto lg:mx-0">
                <div className="space-y-6 lg:space-y-7">
                  {/* Category Name */}
                  <div className="space-y-4">
                    <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight tracking-tight">
                      {category?.name || 'Category'}
                    </h1>
                    <div className="flex items-center justify-center lg:justify-start">
                      <div className="w-16 h-0.5 bg-main-color rounded-full"></div>
                      <div className="w-8 h-0.5 bg-main-color/60 rounded-full ml-2"></div>
                      <div className="w-4 h-0.5 bg-main-color/30 rounded-full ml-2"></div>
                    </div>
                  </div>

                  {/* Category Description */}
                  <div className="max-w-2xl lg:max-w-3xl mx-auto lg:mx-0">
                    {category?.description ? (
                      <p className="text-gray-600 text-lg md:text-xl lg:text-xl leading-relaxed font-normal">
                        {category.description}
                      </p>
                    ) : (
                      <p className="text-gray-600 text-lg md:text-xl lg:text-xl leading-relaxed font-normal">
                        Discover our premium collection of {category?.name?.toLowerCase() || 'beauty'} products,
                        carefully curated for quality and excellence.
                      </p>
                    )}
                  </div>

                  {/* Stats or Additional Info */}
                  <div className="flex flex-wrap items-center justify-center lg:justify-start gap-3 lg:gap-4">
                    <div className="flex items-center gap-2.5 text-gray-700 bg-white/80 backdrop-blur-sm px-4 py-2.5 rounded-full shadow-sm border border-gray-100">
                      <div className="w-2 h-2 bg-main-color rounded-full"></div>
                      <span className="text-sm font-medium">
                        {pagination?.total || 0} Products
                      </span>
                    </div>
                    <div className="flex items-center gap-2.5 text-gray-700 bg-white/80 backdrop-blur-sm px-4 py-2.5 rounded-full shadow-sm border border-gray-100">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                      <span className="text-sm font-medium">Premium Quality</span>
                    </div>
                    <div className="flex items-center gap-2.5 text-gray-700 bg-white/80 backdrop-blur-sm px-4 py-2.5 rounded-full shadow-sm border border-gray-100">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm font-medium">Fast Shipping</span>
                    </div>
                  </div>

                  {/* Call to Action */}
                  {(pagination?.total || 0) > 0 && (
                    <div className="pt-2 lg:pt-4">
                      <button
                        onClick={() => {
                          const productsSection = document.getElementById('products-section');
                          productsSection?.scrollIntoView({ behavior: 'smooth' });
                        }}
                        className="inline-flex items-center gap-3 bg-main-color text-white px-8 py-3.5 rounded-full font-semibold hover:bg-main-color/90 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl group"
                      >
                        <FiShoppingBag size={18} className="group-hover:scale-110 transition-transform duration-300" />
                        <span>Browse Products</span>
                        <div className="w-1.5 h-1.5 bg-white/70 rounded-full animate-pulse"></div>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 py-8 md:py-12">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center text-sm text-gray-500 mb-10 max-w-6xl mx-auto">
          <Link href="/" className="hover:text-main-color transition-colors duration-200 font-medium">
            Home
          </Link>
          <FiChevronRight className="mx-2.5 text-gray-300" size={14} />
          <Link href="/shop" className="hover:text-main-color transition-colors duration-200 font-medium">
            Shop
          </Link>
          <FiChevronRight className="mx-2.5 text-gray-300" size={14} />
          <Link href={`/shop/${mainCategorySlug}`} className="hover:text-main-color transition-colors duration-200 font-medium">
            {mainCategoryName}
          </Link>
          <FiChevronRight className="mx-2.5 text-gray-300" size={14} />
          <span className="font-semibold text-gray-800">{categoryName}</span>
        </nav>

        {/* Products Section */}
        {products && products.length > 0 && (
          <section id="products-section" className="max-w-6xl mx-auto">
            {/* Section Header */}
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-10">
              <div className="mb-6 lg:mb-0">
                <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-3">
                  {categoryName} Products
                </h2>
                <p className="text-gray-600 text-base">
                  Showing {products?.length || 0} of {pagination?.total || 0} products
                </p>
              </div>

              {/* Controls */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                {/* Products per page selector */}
                <div className="flex items-center gap-2">
                  <label htmlFor="limit" className="text-sm font-medium text-gray-700 whitespace-nowrap">
                    Show:
                  </label>
                  <select
                    id="limit"
                    value={currentLimit}
                    onChange={(e) => handleLimitChange(parseInt(e.target.value))}
                    disabled={isLoading}
                    className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color focus:border-transparent disabled:opacity-50"
                  >
                    {limitOptions.map((option) => (
                      <option key={option} value={option}>
                        {option} products
                      </option>
                    ))}
                  </select>
                </div>

                {/* View mode toggle */}
                <div className="flex items-center border border-gray-300 rounded-md overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-main-color text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-50'
                    }`}
                    title="Grid view"
                  >
                    <FiGrid size={16} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 transition-colors ${
                      viewMode === 'list'
                        ? 'bg-main-color text-white'
                        : 'bg-white text-gray-600 hover:bg-gray-50'
                    }`}
                    title="List view"
                  >
                    <FiList size={16} />
                  </button>
                </div>

              </div>
            </div>

            {/* Filters and Products Layout */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Filters Sidebar */}
              <div className="lg:col-span-1">
                <ProductFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  onApplyFilters={handleApplyFilters}
                  isLoading={isLoading}
                  metadata={filterMetadata}
                  showCategoryFilter={false}
                />
              </div>

              {/* Products Content */}
              <div className="lg:col-span-3">
                {/* Loading Overlay */}
            {isLoading && (
              <div className="relative">
                <div className="absolute inset-0 bg-white/70 backdrop-blur-sm z-10 flex items-center justify-center">
                  <div className="flex items-center gap-3 bg-white px-6 py-3 rounded-lg shadow-lg">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-main-color"></div>
                    <span className="text-gray-700 font-medium">Loading products...</span>
                  </div>
                </div>
              </div>
            )}

            {/* Products Display */}
            <div className={`transition-opacity duration-300 ${isLoading ? 'opacity-50' : 'opacity-100'}`}>
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {products.map((product) => (
                    <ProductCard
                      key={product.id}
                      product={product}
                      viewMode="grid"
                      onAddToCart={handleAddToCart}
                      addingToCartId={addingToCartId}
                    />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {products.map((product) => (
                    <ProductCard
                      key={product.id}
                      product={product}
                      viewMode="list"
                      onAddToCart={handleAddToCart}
                      addingToCartId={addingToCartId}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-12 flex flex-col sm:flex-row items-center justify-between gap-4">
                {/* Pagination Info */}
                <div className="text-sm text-gray-600">
                  Showing {((currentPage - 1) * (pagination?.limit || 12)) + 1} to{' '}
                  {Math.min(currentPage * (pagination?.limit || 12), pagination?.total || 0)} of{' '}
                  {pagination?.total || 0} products
                </div>

                {/* Pagination Controls */}
                <div className="flex items-center gap-2">
                  {/* Previous Button */}
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!hasPrevPage || isLoading}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                      hasPrevPage && !isLoading
                        ? 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        : 'border-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <FiChevronLeft size={16} />
                    <span className="hidden sm:inline">Previous</span>
                  </button>

                  {/* Page Numbers */}
                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                      let pageNumber;

                      if (totalPages <= 7) {
                        pageNumber = i + 1;
                      } else if (currentPage <= 4) {
                        pageNumber = i + 1;
                      } else if (currentPage >= totalPages - 3) {
                        pageNumber = totalPages - 6 + i;
                      } else {
                        pageNumber = currentPage - 3 + i;
                      }

                      if (pageNumber < 1 || pageNumber > totalPages) return null;

                      return (
                        <button
                          key={pageNumber}
                          onClick={() => handlePageChange(pageNumber)}
                          disabled={isLoading}
                          className={`w-10 h-10 rounded-lg border transition-colors ${
                            pageNumber === currentPage
                              ? 'border-main-color bg-main-color text-white'
                              : isLoading
                              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      );
                    })}

                    {/* Show ellipsis and last page if needed */}
                    {totalPages > 7 && currentPage < totalPages - 3 && (
                      <>
                        <span className="px-2 text-gray-400">...</span>
                        <button
                          onClick={() => handlePageChange(totalPages)}
                          disabled={isLoading}
                          className={`w-10 h-10 rounded-lg border transition-colors ${
                            isLoading
                              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {totalPages}
                        </button>
                      </>
                    )}
                  </div>

                  {/* Next Button */}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!hasNextPage || isLoading}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors ${
                      hasNextPage && !isLoading
                        ? 'border-gray-300 text-gray-700 hover:bg-gray-50'
                        : 'border-gray-200 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    <span className="hidden sm:inline">Next</span>
                    <FiChevronRight size={16} />
                  </button>
                </div>
              </div>
            )}
              </div>
            </div>
          </section>
        )}

        {/* Empty State */}
        {(!products || products.length === 0) && !isLoading && (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <FiShoppingBag size={32} className="text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">No products found</h3>
              <p className="text-gray-600 mb-6">
                We couldn&apos;t find any products in this category. Try browsing other categories or check back later.
              </p>
              <Link
                href="/shop"
                className="inline-flex items-center px-6 py-3 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
              >
                Browse All Products
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryPageClient;
