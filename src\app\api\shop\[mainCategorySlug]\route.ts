import { NextRequest, NextResponse } from 'next/server';
import { ensureValidSlug } from '@/utils/slugUtils';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Shop Main Category API - Base URL:', API_BASE_URL);
console.log('Shop Main Category API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching main category products by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { mainCategorySlug: string } }
) {
  try {
    const rawSlug = params.mainCategorySlug;
    const slug = ensureValidSlug(rawSlug);
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    console.log(`Shop Main Category API - Raw slug: ${rawSlug}, Normalized slug: ${slug}`);
    console.log(`Shop Main Category API - Fetching products for main category slug: ${slug}`);

    // Forward all query parameters to the backend
    const queryParams = new URLSearchParams();

    // Copy all search parameters from the request
    searchParams.forEach((value, key) => {
      queryParams.append(key, value);
    });

    // Set default values if not provided
    if (!searchParams.has('page')) {
      queryParams.set('page', '1');
    }
    if (!searchParams.has('limit')) {
      queryParams.set('limit', '20');
    }

    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/${slug}?${queryParams.toString()}`;

    console.log(`Shop Main Category API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Disable caching for dynamic filtering
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch products for main category ${slug}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Shop Main Category API - Response received for ${slug}:`, {
      totalProducts: data.pagination?.total || data.data?.length || 0,
      categories: data.categories?.length || 0
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Shop Main Category API error for ${params.mainCategorySlug}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch main category products' },
      { status: 500 }
    );
  }
}
