import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// DELETE handler for deleting a grouped product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`API proxy - Deleting grouped product with ID ${id}`);

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/grouped-products/${id}`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`API proxy - Response status for deleting grouped product ${id}:`, response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy - Error deleting grouped product ${id}:`, errorText);
      return NextResponse.json(
        { error: `Failed to delete grouped product with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (DELETE /grouped-products/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to delete grouped product with ID ${params.id}` },
      { status: 500 }
    );
  }
}

// GET handler for fetching a grouped product by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`API proxy - Fetching grouped product with ID ${id}`);

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/grouped-products/${id}`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`API proxy - Response status for fetching grouped product ${id}:`, response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy - Error fetching grouped product ${id}:`, errorText);
      return NextResponse.json(
        { error: `Failed to fetch grouped product with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (GET /grouped-products/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to fetch grouped product with ID ${params.id}` },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();

    console.log(`API proxy - Updating grouped product with ID ${id}:`, body);

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/grouped-products/${id}`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    console.log(`API proxy - Response status for updating grouped product ${id}:`, response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy - Error updating grouped product ${id}:`, errorText);
      return NextResponse.json(
        { error: `Failed to update grouped product with ID ${id}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (PATCH /grouped-products/${params.id}):`, error);
    return NextResponse.json(
      { error: `Failed to update grouped product with ID ${params.id}` },
      { status: 500 }
    );
  }
}
