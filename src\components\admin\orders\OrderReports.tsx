"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FiTrendingUp,
  FiUsers,
  FiPackage,
  FiDollarSign,
  FiCalendar,
  FiRefreshCw,
  FiDownload,
  FiStar,
  FiShoppingBag,
} from "react-icons/fi";
import { OrderInsights, OrderReports as OrderReportsType } from "@/services/api";
import { ordersApi } from "@/services/ordersApi";
import { 
  <PERSON><PERSON><PERSON>, 
  DoughnutC<PERSON>, 
  createBarChartData, 
  createPieChartData,
  chartColors 
} from "../charts/ChartComponents";

const OrderReports = () => {
  const [insights, setInsights] = useState<OrderInsights | null>(null);
  const [reports, setReports] = useState<OrderReportsType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    dateFrom: '',
    dateTo: '',
  });

  // Fetch reports and insights
  const fetchReportsAndInsights = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const [reportsData, insightsData] = await Promise.all([
        ordersApi.getOrderReports(),
        ordersApi.getOrderInsights(dateRange.dateFrom && dateRange.dateTo ? dateRange : {}),
      ]);

      setReports(reportsData);
      setInsights(insightsData);
    } catch (error) {
      console.error("Error fetching reports and insights:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch reports and insights");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReportsAndInsights();
  }, []);

  // Handle date range change
  const handleDateRangeChange = () => {
    if (dateRange.dateFrom && dateRange.dateTo) {
      fetchReportsAndInsights();
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format number with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Create top products chart data
  const createTopProductsData = () => {
    if (!insights?.topProducts || insights.topProducts.length === 0) return null;

    const topProducts = insights.topProducts.slice(0, 10); // Top 10 products
    return createBarChartData(
      topProducts.map(product => product.productName.length > 20 
        ? product.productName.substring(0, 20) + '...' 
        : product.productName
      ),
      [{
        label: 'Revenue',
        data: topProducts.map(product => product.totalRevenue),
        colors: [chartColors.primary],
      }]
    );
  };

  // Create revenue by status chart data
  const createRevenueByStatusData = () => {
    if (!insights?.revenueByStatus || insights.revenueByStatus.length === 0) return null;

    const statusColors = {
      'COMPLETED': chartColors.success,
      'PENDING': chartColors.warning,
      'PROCESSING': chartColors.info,
      'CANCELLED': chartColors.danger,
      'REFUNDED': chartColors.dark,
    };

    return createPieChartData(
      insights.revenueByStatus.map(item => item.status),
      insights.revenueByStatus.map(item => item.revenue),
      insights.revenueByStatus.map(item => statusColors[item.status as keyof typeof statusColors] || chartColors.light)
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FiPieChart className="text-main-color" size={24} />
          <h2 className="text-2xl font-bold text-gray-900">Reports & Insights</h2>
        </div>
        <div className="flex items-center gap-4">
          {/* Date Range Filter */}
          <div className="flex items-center gap-2">
            <input
              type="date"
              value={dateRange.dateFrom}
              onChange={(e) => setDateRange(prev => ({ ...prev, dateFrom: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color text-sm"
            />
            <span className="text-gray-500">to</span>
            <input
              type="date"
              value={dateRange.dateTo}
              onChange={(e) => setDateRange(prev => ({ ...prev, dateTo: e.target.value }))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color text-sm"
            />
            <button
              onClick={handleDateRangeChange}
              disabled={!dateRange.dateFrom || !dateRange.dateTo}
              className="px-4 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              Apply
            </button>
          </div>
          <button
            onClick={fetchReportsAndInsights}
            disabled={isLoading}
            className="inline-flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50"
          >
            <FiRefreshCw size={16} className={isLoading ? 'animate-spin' : ''} />
            Refresh
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
          <span className="ml-2 text-gray-600">Loading reports and insights...</span>
        </div>
      )}

      {/* Reports Content */}
      {!isLoading && insights && reports && (
        <>
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Revenue</p>
                  <h2 className="text-2xl font-bold mt-2">{formatCurrency(insights.analytics.totalRevenue)}</h2>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <FiDollarSign className="w-6 h-6 text-green-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">
                  {dateRange.dateFrom && dateRange.dateTo 
                    ? `${formatDate(dateRange.dateFrom)} - ${formatDate(dateRange.dateTo)}`
                    : 'All time'
                  }
                </span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Total Orders</p>
                  <h2 className="text-2xl font-bold mt-2">{formatNumber(insights.analytics.totalOrders)}</h2>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <FiShoppingBag className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">
                  Avg: {formatCurrency(insights.analytics.averageOrderValue)}
                </span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Top Products</p>
                  <h2 className="text-2xl font-bold mt-2">{insights.topProducts.length}</h2>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <FiPackage className="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">Products with sales</span>
              </div>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">Top Customers</p>
                  <h2 className="text-2xl font-bold mt-2">{insights.topCustomers.length}</h2>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <FiUsers className="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm text-gray-500">Active customers</span>
              </div>
            </div>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Products Chart */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Products by Revenue</h3>
              {createTopProductsData() ? (
                <BarChart
                  data={createTopProductsData()!}
                  height={400}
                  horizontal={true}
                />
              ) : (
                <div className="flex items-center justify-center h-[400px] text-gray-500">
                  No product data available
                </div>
              )}
            </div>

            {/* Revenue by Status Chart */}
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue by Order Status</h3>
              {createRevenueByStatusData() ? (
                <DoughnutChart
                  data={createRevenueByStatusData()!}
                  height={400}
                  centerText={formatCurrency(insights.analytics.totalRevenue)}
                />
              ) : (
                <div className="flex items-center justify-center h-[400px] text-gray-500">
                  No status data available
                </div>
              )}
            </div>
          </div>

          {/* Top Products and Customers Tables */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top Products Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FiStar className="text-main-color" />
                  Top Products
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Orders
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Revenue
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {insights.topProducts.length === 0 ? (
                      <tr>
                        <td colSpan={3} className="px-6 py-12 text-center">
                          <div className="flex flex-col items-center">
                            <FiPackage className="text-gray-400 mb-4" size={48} />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                            <p className="text-gray-600">No product sales data available for the selected period</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      insights.topProducts.slice(0, 10).map((product, index) => (
                        <tr key={product.productId || index} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8">
                                <div className="h-8 w-8 rounded-lg bg-main-color/10 flex items-center justify-center">
                                  <span className="text-main-color font-semibold text-sm">
                                    {index + 1}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {product.productName}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {formatNumber(product.totalQuantity)} units sold
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            {formatNumber(product.orderCount)}
                          </td>
                          <td className="px-6 py-4 text-sm font-medium text-gray-900">
                            {formatCurrency(product.totalRevenue)}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Top Customers Table */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <FiUsers className="text-main-color" />
                  Top Customers
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Orders
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Spent
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {insights.topCustomers.length === 0 ? (
                      <tr>
                        <td colSpan={3} className="px-6 py-12 text-center">
                          <div className="flex flex-col items-center">
                            <FiUsers className="text-gray-400 mb-4" size={48} />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No customers found</h3>
                            <p className="text-gray-600">No customer data available for the selected period</p>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      insights.topCustomers.slice(0, 10).map((customer, index) => (
                        <tr key={customer.customerId || index} className="hover:bg-gray-50">
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8">
                                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                  <span className="text-blue-600 font-semibold text-sm">
                                    {index + 1}
                                  </span>
                                </div>
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {customer.customerName}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {customer.customerEmail}
                                </div>
                                <div className="text-sm text-gray-500">
                                  Last order: {formatDate(customer.lastOrderDate)}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            {formatNumber(customer.totalOrders)}
                          </td>
                          <td className="px-6 py-4">
                            <div className="text-sm font-medium text-gray-900">
                              {formatCurrency(customer.totalSpent)}
                            </div>
                            <div className="text-sm text-gray-500">
                              Avg: {formatCurrency(customer.averageOrderValue)}
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Period Comparison */}
          {reports && (
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Period Comparison</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-2">Last 24 Hours</div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {formatCurrency(reports.lastDay.totalRevenue)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatNumber(reports.lastDay.totalOrders)} orders
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-2">Last 30 Days</div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {formatCurrency(reports.lastMonth.totalRevenue)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatNumber(reports.lastMonth.totalOrders)} orders
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-2">Last 12 Months</div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {formatCurrency(reports.lastYear.totalRevenue)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatNumber(reports.lastYear.totalOrders)} orders
                  </div>
                </div>
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-2">Last 5 Years</div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {formatCurrency(reports.last5Years.totalRevenue)}
                  </div>
                  <div className="text-sm text-gray-500">
                    {formatNumber(reports.last5Years.totalOrders)} orders
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default OrderReports;
