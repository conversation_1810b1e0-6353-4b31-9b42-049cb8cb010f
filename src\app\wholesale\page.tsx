import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import WholesalePageClient from "@/components/shop/WholesalePageClient";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  stockQuantity: number | null;
  stockStatus: string;
  productType: string;
  discountPercent?: number;
  isOnSale: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  categories?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  imageUrl: string;
}

interface WholesaleData {
  data: Product[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  category?: Category;
  appliedFilters: any;
}

interface WholesalePageProps {
  searchParams: { 
    page?: string; 
    limit?: string;
    [key: string]: string | undefined;
  };
}

// Server-side data fetching
async function getWholesaleData(): Promise<WholesaleData | null> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    // For static generation, only fetch basic data without search params
    const response = await fetch(`${baseUrl}/api/wholesale?page=1&limit=12`, {
      next: { revalidate: 3600 }, // 1 hour cache
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      console.error(`Failed to fetch wholesale data: ${response.status} ${response.statusText}`);
      // During build time, if wholesale endpoint is not available, return null to use fallback
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching wholesale data:', error);
    return null;
  }
}

// Generate dynamic metadata
export async function generateMetadata(): Promise<Metadata> {
  const data = await getWholesaleData();
  
  const title = "Wholesale Beauty Products & Bulk Cosmetics | CocoJojo";
  const baseDescription = "Premium wholesale beauty products and bulk cosmetics for businesses. Competitive pricing, reliable supply, and exceptional quality. Perfect for retailers, salons, and distributors.";
  const description = data?.category?.description || baseDescription;

  return {
    title,
    description,
    keywords: "wholesale cosmetics, bulk beauty products, business beauty supplies, cosmetic distributor, bulk skincare, wholesale makeup, beauty product supplier, cosmetic manufacturing",
    openGraph: {
      title,
      description,
      type: "website",
      url: "/wholesale",
      images: [
        {
          url: data?.category?.imageUrl || "/images/wholesale-og.jpg",
          width: 1200,
          height: 630,
          alt: "CocoJojo Wholesale Beauty Products",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [data?.category?.imageUrl || "/images/wholesale-og.jpg"],
    },
    alternates: {
      canonical: "/wholesale",
    },
  };
}

export default async function WholesalePage({ searchParams }: WholesalePageProps) {
  // Fetch data server-side for SEO (static generation)
  const data = await getWholesaleData();

  // If no data during build time, provide fallback structure
  if (!data) {
    const fallbackData: WholesaleData = {
      data: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 12,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      category: {
        id: 0,
        name: "Wholesale",
        slug: "wholesale",
        description: "Premium bulk cosmetics and beauty products for businesses. Competitive pricing, reliable supply, and exceptional quality.",
        imageUrl: "/images/wholesale-hero.jpg"
      },
      appliedFilters: {}
    };

    return (
      <>
        {/* Structured Data for fallback */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "CollectionPage",
              "name": "Wholesale Beauty Products",
              "description": "Premium wholesale beauty products and bulk cosmetics for businesses",
              "url": `${process.env.VERCEL_URL}/wholesale`,
              "breadcrumb": {
                "@type": "BreadcrumbList",
                "itemListElement": [
                  {
                    "@type": "ListItem",
                    "position": 1,
                    "name": "Home",
                    "item": process.env.VERCEL_URL
                  },
                  {
                    "@type": "ListItem",
                    "position": 2,
                    "name": "Wholesale",
                    "item": `${process.env.VERCEL_URL}/wholesale`
                  }
                ]
              }
            })
          }}
        />

        <Suspense fallback={
          <div className="min-h-screen bg-gray-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
          </div>
        }>
          <WholesalePageClient initialData={fallbackData} />
        </Suspense>
      </>
    );
  }

  // Clean HTML from descriptions for structured data
  const cleanDescription = (html: string) => {
    return html?.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim() || '';
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": "Wholesale Beauty Products",
            "description": data.category?.description || "Premium wholesale beauty products and bulk cosmetics for businesses",
            "url": `${process.env.VERCEL_URL}/wholesale`,
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": data.pagination.total,
              "itemListElement": data.data.map((product, index) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "description": cleanDescription(product.shortDescription || ''),
                "image": product.imageUrl,
                "sku": product.sku,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
                  "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": process.env.VERCEL_URL
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Wholesale",
                  "item": `${process.env.VERCEL_URL}/wholesale`
                }
              ]
            },
            "potentialAction": {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${process.env.VERCEL_URL}/wholesale?search={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          })
        }}
      />

      <Suspense fallback={
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
        </div>
      }>
        <WholesalePageClient initialData={data} />
      </Suspense>
    </>
  );
}

// Enable ISR with 1 hour revalidation for wholesale page
export const revalidate = 3600;
