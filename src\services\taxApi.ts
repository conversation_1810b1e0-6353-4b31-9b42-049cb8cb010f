// Tax Settings API service

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';

// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  // Log the response status and URL for debugging
  console.log(`API Response: ${response.status} ${response.url}`);

  if (!response.ok) {
    // Try to get error details from the response
    let errorMessage = `Error: ${response.status} ${response.statusText}`;
    let errorDetails = '';

    try {
      // Only try to parse JSON if the content type is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json();
        console.error('API Error Response (JSON):', errorData);
        errorMessage = errorData.message || errorMessage;
        errorDetails = JSON.stringify(errorData, null, 2);
      } else {
        // For non-JSON responses, try to get the text
        const text = await response.text();
        console.error('API Error Response (Text):', text);
        if (text) {
          errorMessage = `${errorMessage} - ${text}`;
          errorDetails = text;
        }
      }
    } catch (e) {
      console.error('Error parsing error response:', e);
      // If we can't parse the error response, use the default message
    }

    console.error(`API Error: ${errorMessage}`);
    if (errorDetails) {
      console.error(`API Error Details: ${errorDetails}`);
    }

    throw new Error(errorMessage);
  }

  try {
    const jsonData = await response.json();
    console.log('Parsed JSON response:', jsonData);
    return jsonData;
  } catch (e) {
    console.error('Error parsing JSON response:', e);
    return {} as T;
  }
};

// Tax Settings interface
export interface TaxSettings {
  id: number;
  name: string;
  value: string;
  createdAt: string;
  updatedAt: string;
}

// Tax API service
export const taxApi = {
  // Get tax settings
  getTaxSettings: async (): Promise<TaxSettings> => {
    try {
      console.log('Fetching tax settings...');

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/tax';

      // Add a cache-busting query parameter to avoid browser caching
      const cacheBuster = `?_=${Date.now()}`;
      const response = await fetch(`${proxyUrl}${cacheBuster}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      });
      return await handleResponse<TaxSettings>(response);
    } catch (error) {
      console.error('Failed to fetch tax settings:', error);
      throw error;
    }
  },

  // Update tax settings
  updateTaxSettings: async (taxSettings: Partial<TaxSettings>): Promise<TaxSettings> => {
    try {
      console.log('Updating tax settings:', taxSettings);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/tax';

      const response = await fetch(proxyUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
        body: JSON.stringify(taxSettings),
      });
      return await handleResponse<TaxSettings>(response);
    } catch (error) {
      console.error('Failed to update tax settings:', error);
      throw error;
    }
  },
};

export default taxApi;
