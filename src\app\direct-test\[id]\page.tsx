"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price: number;
  // Add other fields as needed
}

export default function DirectTestPage({ params }: { params: { id: string } }) {
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        console.log(`Direct Test Page - Fetching product with ID/slug: ${params.id}`);

        // Determine if the ID is numeric or a slug
        const isNumeric = /^\d+$/.test(params.id);

        // Use the appropriate API endpoint
        let apiUrl;
        if (isNumeric) {
          // If it's numeric, use the ID endpoint
          apiUrl = `/api/direct-test/${params.id}`;
        } else {
          // If it's a slug, use the slug endpoint
          apiUrl = `/api/shop/products/slug/${params.id}`;
        }

        console.log(`Direct Test Page - Using API URL: ${apiUrl}`);

        const response = await fetch(apiUrl, {
          cache: 'no-store'
        });

        if (!response.ok) {
          console.error(`Direct Test Page - Product not found: ${params.id}`);
          setError(`Product not found: ${params.id}`);
          setLoading(false);
          return;
        }

        const data = await response.json();
        console.log('Direct Test Page - Product data received:', data);
        setProduct(data);
      } catch (err) {
        console.error('Direct Test Page - Error fetching product:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [params.id]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error || !product) {
    return (
      <div>
        <h1>Error Loading Product</h1>
        <p>{error || 'Product not found'}</p>
        <button onClick={() => router.push('/')}>Go Home</button>
      </div>
    );
  }

  return (
    <div>
      <h1>Direct Test Product Page</h1>
      <h2>{product.name}</h2>
      <p>ID: {product.id}</p>
      <p>Slug: {product.slug}</p>
      <p>Price: ${product.price}</p>
      <div dangerouslySetInnerHTML={{ __html: product.description }} />
      <button onClick={() => router.push(`/shop/${product.slug}`)}>
        View in Shop
      </button>
    </div>
  );
}
