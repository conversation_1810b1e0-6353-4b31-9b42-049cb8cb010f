import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// PUT handler for updating grouped product base
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    const body = await request.json();

    console.log(`API proxy - Updating grouped product base with ID ${id}:`, body);

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/grouped-products/${id}/base`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy error - Failed to update grouped product base (${response.status}):`, errorText);
      return NextResponse.json(
        { error: `Failed to update grouped product base: ${errorText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`API proxy - Successfully updated grouped product base:`, data);
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (PUT /grouped-products/[id]/base):', error);
    return NextResponse.json(
      { error: 'Failed to update grouped product base' },
      { status: 500 }
    );
  }
}
