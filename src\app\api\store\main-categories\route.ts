import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Main Categories API - Base URL:', API_BASE_URL);
console.log('Main Categories API - Path Prefix:', API_PATH_PREFIX);

// GET handler for fetching all main categories
export async function GET(request: NextRequest) {
  try {
    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/main-categories`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    let data;
    try {
      data = await response.json();
      console.log('Main Categories API response:', data);
    } catch (e) {
      console.error('Error parsing JSON response for main categories:', e);
      // Return empty array if we can't parse the response
      return NextResponse.json([]);
    }

    // If the response is not an array, convert it to an empty array
    if (!Array.isArray(data)) {
      console.warn(`Expected array but got ${typeof data} for main categories`);
      return NextResponse.json([]);
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /main-categories):', error);
    return NextResponse.json(
      { error: 'Failed to fetch main categories' },
      { status: 500 }
    );
  }
}

// POST handler for creating a new main category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/main-categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /main-categories):', error);
    return NextResponse.json(
      { error: 'Failed to create main category' },
      { status: 500 }
    );
  }
}
