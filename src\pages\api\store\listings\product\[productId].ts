import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method, query } = req;
  const { productId } = query;
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3300';

  if (!productId) {
    return res.status(400).json({ message: 'Product ID is required' });
  }

  try {
    switch (method) {
      case 'GET': {
        // Get all listings for a product
        const response = await fetch(`${baseUrl}/store/listings/product/${productId}`, {
          method: 'GET',
        });

        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.message || 'Failed to fetch listings');
        }
        
        return res.status(response.status).json(data);
      }

      default:
        res.setHeader('Allow', ['GET']);
        return res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error(`API error in /api/store/listings/product/${productId}:`, error);
    return res.status(500).json({ message: error instanceof Error ? error.message : 'An error occurred' });
  }
}
