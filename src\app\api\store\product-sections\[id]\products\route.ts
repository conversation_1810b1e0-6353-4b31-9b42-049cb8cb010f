import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Product Section Products API - Base URL:', API_BASE_URL);
console.log('Product Section Products API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// POST handler for adding a product to a section
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const sectionId = params.id;
    const body = await request.json();
    console.log(`Adding product to section ID ${sectionId}:`, body);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections/${sectionId}/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error(`Failed to add product to section ID ${sectionId}: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to add product to section ID ${sectionId}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Successfully added product to section ID ${sectionId}`);
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API proxy error (POST /product-sections/${params.id}/products):`, error);
    return NextResponse.json(
      { error: `Failed to add product to section ID ${params.id}` },
      { status: 500 }
    );
  }
}
