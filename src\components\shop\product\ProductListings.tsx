"use client";

import { useState } from 'react';
import ProductReviews from './ProductReviews';

interface ProductListing {
  id: number;
  title: string;
  content: string;
}

interface ProductListingsProps {
  listings: ProductListing[];
  productId?: number;
}

const ProductListings = ({ listings, productId }: ProductListingsProps) => {
  const [activeTab, setActiveTab] = useState(0);

  // Create tabs array with listings and reviews
  const tabs = [
    ...listings.map(listing => ({
      id: listing.id,
      title: listing.title,
      content: listing.content,
      type: 'listing' as const
    })),
    {
      id: 'reviews',
      title: 'Reviews',
      content: '',
      type: 'reviews' as const
    }
  ];

  // If no listings, show only reviews tab
  if (listings.length === 0) {
    return (
      <div>
        <h2 className="text-2xl font-medium text-gray-800 mb-6">Reviews</h2>
        <ProductReviews productId={productId || 1} />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-2xl font-medium text-gray-800 mb-6">Product Information</h2>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          {tabs.map((tab, index) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(index)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === index
                  ? "border-main-color text-main-color"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.title}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="prose max-w-none">
        {tabs[activeTab] && (
          <div>
            {tabs[activeTab].type === 'listing' ? (
              <div
                dangerouslySetInnerHTML={{
                  __html: tabs[activeTab].content
                }}
              />
            ) : (
              <ProductReviews productId={productId || 1} />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductListings;
