"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

const slides = [
  {
    id: 1,
    title: "Premium Organic Beauty",
    subtitle: "Embrace Your Natural Beauty",
    description: "Discover our collection of certified organic cosmetics made with pure, natural ingredients for radiant, healthy skin.",
    img: "/images/first.jpg",
    url: "/shop",
    bg: "bg-gradient-to-br from-rose-50 via-pink-50 to-orange-50",
    ctaText: "Shop Now",
    ctaSecondary: "Learn More",
    ctaSecondaryUrl: "/about"
  },
  {
    id: 2,
    title: "Wholesale & Bulk Solutions",
    subtitle: "For Businesses Worldwide",
    description: "Professional-grade cosmetic products and manufacturing services for retailers, spas, and beauty brands globally.",
    img: "https://www.organicpureoil.com/wp-content/uploads/2023/04/Rectangle-107-min-1-1-1-1-1.png",
    url: "/shop",
    bg: "bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",
    ctaText: "Get Quote",
    ctaSecondary: "View Catalog",
    ctaSecondaryUrl: "/shop"
  },
  {
    id: 3,
    title: "Contract Manufacturing",
    subtitle: "Your Vision, Our Expertise",
    description: "Complete manufacturing solutions including custom formulation, private labeling, packaging, and filling services.",
    img: "/images/frutti_banner.png",
    url: "/contact",
    bg: "bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50",
    ctaText: "Start Project",
    ctaSecondary: "Our Services",
    ctaSecondaryUrl: "/about"
  },
];

const Slider = () => {
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrent((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative h-[calc(100vh-80px)] min-h-[600px] overflow-hidden">
      <div
        className="w-max h-full flex transition-all ease-in-out duration-1000"
        style={{ transform: `translateX(-${current * 100}vw)` }}
      >
        {slides.map((slide) => (
          <div
            className={`${slide.bg} w-screen h-full flex flex-col xl:flex-row relative`}
            key={slide.id}
          >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-0 left-0 w-96 h-96 bg-gray-900 rounded-full -translate-x-48 -translate-y-48"></div>
              <div className="absolute bottom-0 right-0 w-96 h-96 bg-gray-900 rounded-full translate-x-48 translate-y-48"></div>
            </div>

            {/* TEXT CONTAINER */}
            <div className="relative z-10 h-1/2 xl:w-1/2 xl:h-full flex flex-col items-center justify-center gap-6 lg:gap-8 text-center px-4 md:px-8 lg:px-16">
              <div className="max-w-2xl">
                <span className="inline-block bg-main-color/10 text-main-color px-4 py-2 rounded-full text-sm font-semibold mb-4">
                  {slide.subtitle}
                </span>
                <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 mb-6 leading-tight">
                  {slide.title}
                </h1>
                <p className="text-lg md:text-xl text-gray-700 mb-8 leading-relaxed">
                  {slide.description}
                </p>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href={slide.url}>
                    <button className="bg-main-color text-white px-8 py-4 rounded-lg font-semibold hover:bg-main-color/90 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
                      {slide.ctaText}
                    </button>
                  </Link>
                  <Link href={slide.ctaSecondaryUrl}>
                    <button className="border-2 border-main-color text-main-color px-8 py-4 rounded-lg font-semibold hover:bg-main-color hover:text-white transition-all duration-300 hover:scale-105">
                      {slide.ctaSecondary}
                    </button>
                  </Link>
                </div>
              </div>
            </div>

            {/* IMAGE CONTAINER */}
            <div className="h-1/2 xl:w-1/2 xl:h-full relative">
              <Image
                src={slide.img}
                alt={slide.title}
                fill
                sizes="(max-width: 1280px) 100vw, 50vw"
                className="object-cover"
                priority={slide.id === 1}
              />
              <div className="absolute inset-0 bg-gradient-to-t xl:bg-gradient-to-r from-black/20 to-transparent"></div>
            </div>
          </div>
        ))}
      </div>

      {/* Navigation Dots */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-3 z-20">
        {slides.map((slide, index) => (
          <button
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              current === index
                ? "bg-main-color scale-125 shadow-lg"
                : "bg-white/50 hover:bg-white/80"
            }`}
            key={slide.id}
            onClick={() => setCurrent(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>

      {/* Navigation Arrows */}
      <button
        onClick={() => setCurrent(current === 0 ? slides.length - 1 : current - 1)}
        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-20"
        aria-label="Previous slide"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button
        onClick={() => setCurrent(current === slides.length - 1 ? 0 : current + 1)}
        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-20"
        aria-label="Next slide"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  );
};

export default Slider;