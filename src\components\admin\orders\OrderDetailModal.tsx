"use client";

import { useState, useEffect } from "react";
import {
  FiX,
  FiUser,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiDollarSign,
  FiShoppingBag,
  FiPackage,
  FiTruck,
  FiCreditCard,
  FiTag,
  FiHome,
} from "react-icons/fi";
import { Order } from "@/services/api";
import { ordersApi } from "@/services/ordersApi";

interface OrderDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: number | null;
}

const OrderDetailModal: React.FC<OrderDetailModalProps> = ({
  isOpen,
  onClose,
  orderId,
}) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch order details
  useEffect(() => {
    if (isOpen && orderId) {
      fetchOrderDetails();
    }
  }, [isOpen, orderId]);

  const fetchOrderDetails = async () => {
    if (!orderId) return;

    setIsLoading(true);
    setError(null);

    try {
      const orderData = await ordersApi.getOrderById(orderId);
      setOrder(orderData);
    } catch (error) {
      console.error("Error fetching order details:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch order details");
    } finally {
      setIsLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      case 'REFUNDED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FiShoppingBag className="text-main-color" size={24} />
                <h3 className="text-lg font-medium text-gray-900">
                  {order ? `Order #${order.id}` : 'Order Details'}
                </h3>
                {order && (
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(order.status)}`}>
                    {order.status}
                  </span>
                )}
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <FiX size={24} />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white px-6 py-4 max-h-[80vh] overflow-y-auto">
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
                <span className="ml-2 text-gray-600">Loading order details...</span>
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">Error</h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>{error}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {order && (
              <div className="space-y-6">
                {/* Order Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <FiDollarSign className="text-green-600" size={16} />
                      <span className="text-sm font-medium text-gray-700">Total Amount</span>
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(order.totalAmount)}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <FiCalendar className="text-blue-600" size={16} />
                      <span className="text-sm font-medium text-gray-700">Order Date</span>
                    </div>
                    <p className="text-lg font-semibold text-gray-900">{formatDate(order.dateCreated)}</p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <FiPackage className="text-purple-600" size={16} />
                      <span className="text-sm font-medium text-gray-700">Items</span>
                    </div>
                    <p className="text-lg font-semibold text-gray-900">{order.items.length} item{order.items.length !== 1 ? 's' : ''}</p>
                  </div>
                </div>

                {/* Customer Information */}
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <FiUser className="text-main-color" />
                    Customer Information
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h5 className="font-medium text-gray-700 mb-3">Billing Address</h5>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p className="font-medium text-gray-900">
                          {order.firstName && order.lastName 
                            ? `${order.firstName} ${order.lastName}` 
                            : order.name
                          }
                        </p>
                        {order.companyName && (
                          <p className="flex items-center gap-1">
                            <FiHome size={12} />
                            {order.companyName}
                          </p>
                        )}
                        <p className="flex items-center gap-1">
                          <FiMail size={12} />
                          {order.email}
                        </p>
                        <p className="flex items-center gap-1">
                          <FiPhone size={12} />
                          {order.phone}
                        </p>
                        <div className="flex items-start gap-1">
                          <FiMapPin size={12} className="mt-0.5" />
                          <div>
                            <p>{order.streetAddress}</p>
                            <p>{order.city}, {order.state} {order.zipCode}</p>
                            <p>{order.country}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-700 mb-3">Shipping Address</h5>
                      <div className="space-y-2 text-sm text-gray-600">
                        <p className="font-medium text-gray-900">
                          {order.shippingFirstName && order.shippingLastName 
                            ? `${order.shippingFirstName} ${order.shippingLastName}` 
                            : order.shippingName
                          }
                        </p>
                        {order.shippingCompany && (
                          <p className="flex items-center gap-1">
                            <FiHome size={12} />
                            {order.shippingCompany}
                          </p>
                        )}
                        <p className="flex items-center gap-1">
                          <FiMail size={12} />
                          {order.shippingEmail}
                        </p>
                        <p className="flex items-center gap-1">
                          <FiPhone size={12} />
                          {order.shippingPhone}
                        </p>
                        <div className="flex items-start gap-1">
                          <FiMapPin size={12} className="mt-0.5" />
                          <div>
                            <p>{order.shippingStreet}</p>
                            <p>{order.shippingCity}, {order.shippingState} {order.shippingZipCode}</p>
                            <p>{order.shippingCountry}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Order Items */}
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <FiPackage className="text-main-color" />
                      Order Items
                    </h4>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Product
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Quantity
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Net Revenue
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Gross Revenue
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {order.items.map((item) => (
                          <tr key={item.id}>
                            <td className="px-6 py-4">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
                                    <FiPackage className="text-gray-600" size={20} />
                                  </div>
                                </div>
                                <div className="ml-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {item.product ? item.product.name : item.itemName || 'Unknown Product'}
                                  </div>
                                  {item.product && (
                                    <div className="text-sm text-gray-500">
                                      SKU: {item.product.sku}
                                    </div>
                                  )}
                                  {item.variationId && (
                                    <div className="text-sm text-gray-500">
                                      Variation ID: {item.variationId}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {item.quantity}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {formatCurrency(item.productNetRevenue)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900">
                              {formatCurrency(item.productGrossRevenue)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* Payment & Shipping Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Payment Summary */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <FiCreditCard className="text-main-color" />
                      Payment Summary
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Subtotal:</span>
                        <span className="font-medium">
                          {formatCurrency(order.totalAmount - order.taxAmount - order.shippingAmount - order.shippingTaxAmount + order.couponAmount)}
                        </span>
                      </div>
                      {order.couponAmount > 0 && (
                        <div className="flex justify-between text-green-600">
                          <span className="flex items-center gap-1">
                            <FiTag size={14} />
                            Coupon Discount:
                          </span>
                          <span className="font-medium">-{formatCurrency(order.couponAmount)}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tax:</span>
                        <span className="font-medium">{formatCurrency(order.taxAmount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Shipping:</span>
                        <span className="font-medium">{formatCurrency(order.shippingAmount)}</span>
                      </div>
                      {order.shippingTaxAmount > 0 && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Shipping Tax:</span>
                          <span className="font-medium">{formatCurrency(order.shippingTaxAmount)}</span>
                        </div>
                      )}
                      <div className="border-t border-gray-300 pt-3">
                        <div className="flex justify-between text-lg font-bold">
                          <span>Total:</span>
                          <span>{formatCurrency(order.totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Order Timeline */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <FiCalendar className="text-main-color" />
                      Order Timeline
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-main-color rounded-full"></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">Order Created</p>
                          <p className="text-sm text-gray-600">{formatDate(order.createdAt)}</p>
                        </div>
                      </div>
                      {order.updatedAt !== order.createdAt && (
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div>
                            <p className="text-sm font-medium text-gray-900">Last Updated</p>
                            <p className="text-sm text-gray-600">{formatDate(order.updatedAt)}</p>
                          </div>
                        </div>
                      )}
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          order.status === 'COMPLETED' ? 'bg-green-500' :
                          order.status === 'CANCELLED' ? 'bg-red-500' :
                          order.status === 'PROCESSING' ? 'bg-blue-500' :
                          'bg-yellow-500'
                        }`}></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">Current Status</p>
                          <p className="text-sm text-gray-600">{order.status}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Coupon Information */}
                {order.coupon && (
                  <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-800 mb-2 flex items-center gap-2">
                      <FiTag className="text-green-600" />
                      Coupon Applied
                    </h4>
                    <div className="text-sm text-green-700">
                      <p><strong>Code:</strong> {order.coupon.code}</p>
                      <p><strong>Discount:</strong> {formatCurrency(order.coupon.amount)}</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-6 py-3 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailModal;
