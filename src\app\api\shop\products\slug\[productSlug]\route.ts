import { NextRequest, NextResponse } from 'next/server';
import { ensureValidSlug } from '@/utils/slugUtils';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://coco-jojo-api-production.up.railway.app';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Shop Product API - Base URL:', API_BASE_URL);
console.log('Shop Product API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

export async function GET(
  request: NextRequest,
  { params }: { params: { productSlug: string } }
) {
  try {
    // Extract and normalize the product slug
    const rawProductSlug = params.productSlug;
    const productSlug = ensureValidSlug(rawProductSlug);

    // Get password from query parameters using nextUrl to avoid dynamic server usage
    const { searchParams } = request.nextUrl;
    const password = searchParams.get('password');

    console.log(`Shop Product API - Raw slug: ${rawProductSlug}, Normalized: ${productSlug}, Password provided: ${!!password}`);

    // Construct the backend URL - matches backend pattern /shop/products/slug/:slug
    let backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products/slug/${productSlug}`;

    // Add password parameter if provided
    if (password) {
      backendUrl += `?password=${encodeURIComponent(password)}`;
    }

    console.log(`Shop Product API - Calling backend URL: ${backendUrl.replace(/password=[^&]*/, 'password=***')}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Remove cache control to make it dynamic since we need to handle passwords
      cache: 'no-store'
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch product with slug ${productSlug}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Shop Product API - Response received for ${productSlug}:`, {
      productId: data.data?.id || data.id,
      productName: data.data?.name || data.name
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error('Shop Product API - Error:', error);
    return NextResponse.json(
      { error: 'Internal server error while fetching product' },
      { status: 500 }
    );
  }
}
