"use client";

import { useMemo } from 'react';
import { BlogPost } from '@/types/blog';
import { allBlogPosts } from '@/data/blogData';
import BlogCard from './BlogCard';

interface RelatedPostsProps {
  currentPost: BlogPost;
  maxPosts?: number;
}

export default function RelatedPosts({ currentPost, maxPosts = 3 }: RelatedPostsProps) {
  const relatedPosts = useMemo(() => {
    // Filter out the current post and unpublished posts
    const otherPosts = allBlogPosts.filter(
      post => post.id !== currentPost.id && post.published
    );

    // Calculate relevance score based on shared tags
    const postsWithScore = otherPosts.map(post => {
      const sharedTags = post.tags.filter(tag => currentPost.tags.includes(tag));
      const score = sharedTags.length;
      return { post, score };
    });

    // Sort by relevance score (descending) and then by date (newest first)
    postsWithScore.sort((a, b) => {
      if (a.score !== b.score) {
        return b.score - a.score;
      }
      return new Date(b.post.createdDate).getTime() - new Date(a.post.createdDate).getTime();
    });

    // Return the top posts
    return postsWithScore.slice(0, maxPosts).map(item => item.post);
  }, [currentPost, maxPosts]);

  if (relatedPosts.length === 0) {
    return null;
  }

  return (
    <section className="bg-white rounded-lg shadow-sm border p-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {relatedPosts.map((post) => (
          <BlogCard key={post.id} post={post} />
        ))}
      </div>
    </section>
  );
}
