import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import { Suspense } from "react";
import CategoryPageClient from "@/components/shop/CategoryPageClient";
import { ensureValidSlug } from "@/utils/slugUtils";
import { ShopProduct } from "@/types/product";

// Types
interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
  description?: string;
}

// Using ShopProduct from shared types

interface CategoryPageProps {
  params: {
    mainCategorySlug: string;
    categorySlug: string;
  };
}

// Server-side data fetching
async function getCategoryData(mainCategorySlug: string, categorySlug: string, page: number = 1, limit: number = 20) {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    // Fetch category data and products with pagination
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${baseUrl}/api/shop/${mainCategorySlug}/${categorySlug}?${queryParams.toString()}`, {
      next: { revalidate: 7200 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Failed to fetch category data for ${mainCategorySlug}/${categorySlug}: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching category data:', error);
    return null;
  }
}



// Generate static params for all categories
export async function generateStaticParams({ params }: { params: { mainCategorySlug: string } }) {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    // Get categories for this main category
    const response = await fetch(`${baseUrl}/api/shop/${params.mainCategorySlug}`, {
      next: { revalidate: 7200 } // Cache for 10 minutes
    });

    if (!response.ok) {
      console.error(`Failed to fetch categories for ${params.mainCategorySlug}: ${response.status} ${response.statusText}`);
      // Return empty array instead of throwing error to prevent build failure
      return [];
    }

    const data = await response.json();
    const categories = data.categories || [];

    // Only return categories that actually exist
    const validCategories = categories.filter((category: Category) =>
      category && category.slug && category.slug.trim() !== ''
    );

    return validCategories.map((category: Category) => ({
      categorySlug: category.slug,
    }));
  } catch (error) {
    console.error('Error generating static params for categories:', error);
    // Return empty array instead of throwing error to prevent build failure
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const normalizedMainSlug = ensureValidSlug(params.mainCategorySlug);
  const normalizedCategorySlug = ensureValidSlug(params.categorySlug);

  const categoryData = await getCategoryData(normalizedMainSlug, normalizedCategorySlug);

  if (!categoryData) {
    return {
      title: 'Category Not Found | CocoJojo',
      description: 'The requested category could not be found.',
    };
  }

  // Extract category from the response - use the category object if available, otherwise fallback to categories array
  const category = categoryData.category || categoryData.categories?.[0] || {
    name: normalizedCategorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    slug: normalizedCategorySlug
  };

  // Extract main category from the response - since backend doesn't return mainCategory object, create from slug
  const mainCategory = {
    name: normalizedMainSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    slug: normalizedMainSlug
  };
  const products = categoryData.data || [];
  const productCount = categoryData.pagination?.total || products.length;

  // Extract tags from products for keywords
  const allTags = products.flatMap((product: ShopProduct) => (product.tags || []).map((tag: any) => tag.name));
  const uniqueTagsSet = new Set(allTags);
  const uniqueTags = Array.from(uniqueTagsSet).slice(0, 10).join(', ');

  return {
    title: `${category.name} - ${mainCategory.name} | CocoJojo`,
    description: category.description || `Shop ${productCount} premium ${category.name.toLowerCase()} products in our ${mainCategory.name.toLowerCase()} collection. ${uniqueTags ? `Including ${uniqueTags}.` : ''} Free shipping on orders over $50.`,
    keywords: `${category.name.toLowerCase()}, ${mainCategory.name.toLowerCase()}, organic beauty, natural skincare, ${uniqueTags}`,
    openGraph: {
      title: `${category.name} - ${mainCategory.name} | CocoJojo`,
      description: category.description || `Shop ${productCount} premium ${category.name.toLowerCase()} products in our ${mainCategory.name.toLowerCase()} collection.`,
      type: "website",
      url: `/shop/${normalizedMainSlug}/${normalizedCategorySlug}`,
      images: [
        {
          url: category.imageUrl || "/images/category-og.jpg",
          width: 1200,
          height: 630,
          alt: `${category.name} - CocoJojo`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${category.name} - ${mainCategory.name} | CocoJojo`,
      description: `Shop ${productCount} premium ${category.name.toLowerCase()} products.`,
    },
    alternates: {
      canonical: `/shop/${normalizedMainSlug}/${normalizedCategorySlug}`,
    },
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  // Normalize slug parameters
  const normalizedMainSlug = ensureValidSlug(params.mainCategorySlug);
  const normalizedCategorySlug = ensureValidSlug(params.categorySlug);

  // Fetch data server-side for SEO
  const categoryData = await getCategoryData(normalizedMainSlug, normalizedCategorySlug);

  if (!categoryData) {
    notFound();
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": categoryData.category?.name || categoryData.categories?.[0]?.name || normalizedCategorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            "description": categoryData.category?.description || `Premium organic ${categoryData.category?.name?.toLowerCase() || categoryData.categories?.[0]?.name?.toLowerCase() || 'beauty'} products`,
            "url": `${process.env.VERCEL_URL}/shop/${normalizedMainSlug}/${normalizedCategorySlug}`,
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": categoryData.pagination?.total || categoryData.data?.length || 0,
              "itemListElement": (categoryData.data || []).slice(0, 10).map((product: ShopProduct, index: number) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "url": `${process.env.VERCEL_URL}/shop/${params.mainCategorySlug}/${params.categorySlug}/${product.slug}`,
                "image": product.imageUrl,
                "description": product.shortDescription,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": process.env.VERCEL_URL
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": `${process.env.VERCEL_URL}/shop`
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": normalizedMainSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                  "item": `${process.env.VERCEL_URL}/shop/${normalizedMainSlug}`
                },
                {
                  "@type": "ListItem",
                  "position": 4,
                  "name": categoryData.category?.name || categoryData.categories?.[0]?.name || normalizedCategorySlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                  "item": `${process.env.VERCEL_URL}/shop/${normalizedMainSlug}/${normalizedCategorySlug}`
                }
              ]
            }
          })
        }}
      />

      <Suspense fallback={<div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
      </div>}>
        <CategoryPageClient
          initialData={categoryData}
          mainCategoryData={undefined}
          mainCategorySlug={normalizedMainSlug}
          categorySlug={normalizedCategorySlug}
        />
      </Suspense>
    </>
  );
}

// Force dynamic rendering for filtering functionality
export const dynamic = 'force-dynamic';

// Disable static generation for shop pages to enable real-time filtering
export const revalidate = 0;
