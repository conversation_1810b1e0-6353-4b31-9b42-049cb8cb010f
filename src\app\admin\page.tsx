"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/hooks/useAuthStore";
import { FiAlertCircle } from "react-icons/fi";

const AdminLogin = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const router = useRouter();
  const { login, isAuthenticated, isLoading, user, getProfile, error: authError } = useAuthStore();

  useEffect(() => {
    const checkAdminAuth = async () => {
      // If user is already authenticated, check if they're an admin
      if (isAuthenticated()) {
        // If we already have user data, check admin status
        if (user) {
          if (user.isAdmin || user.isSuperAdmin) {
            router.push("/admin/dashboard");
          }
        } else {
          // If no user data, fetch profile
          try {
            const userData = await getProfile();
            if (userData && (userData.isAdmin || userData.isSuperAdmin)) {
              router.push("/admin/dashboard");
            }
          } catch (error) {
            console.error("Error fetching user profile:", error);
          }
        }
      }
    };

    checkAdminAuth();
  }, [isAuthenticated, router, user, getProfile]);

  useEffect(() => {
    // Set error from auth store
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    try {
      const userData = await login(email, password);

      if (userData) {
        if (userData.isAdmin || userData.isSuperAdmin) {
          // Add a small delay to ensure the token is properly stored
          setTimeout(() => {
            router.push("/admin/dashboard");
          }, 100);
        } else {
          setError("You don't have admin privileges");
        }
      } else {
        setError("Invalid credentials");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred during login");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Admin Login
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md flex items-center mb-4">
              <FiAlertCircle className="mr-2 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {isLoading && (
            <div className="text-center text-gray-600">
              <div className="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-main-color mr-2"></div>
              Logging in...
            </div>
          )}
          <div className="rounded-md shadow-sm space-y-4">
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-main-color focus:border-main-color focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                required
                className="appearance-none rounded-lg relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-main-color focus:border-main-color focus:z-10 sm:text-sm"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color ${
                isLoading ? 'opacity-70 cursor-not-allowed' : ''
              }`}
            >
              {isLoading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminLogin;


