"use client";

import { FiLock, FiEyeOff } from "react-icons/fi";

interface ProductAccessIndicatorProps {
  access: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ProductAccessIndicator = ({ 
  access, 
  size = 'sm', 
  className = "" 
}: ProductAccessIndicatorProps) => {
  if (access === 'PUBLIC') {
    return null; // Don't show anything for public products
  }

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 10,
    md: 12,
    lg: 14
  };

  if (access === 'PROTECTED') {
    return (
      <span className={`
        inline-flex items-center gap-1 
        bg-yellow-100 text-yellow-800 
        font-medium rounded-full
        ${sizeClasses[size]}
        ${className}
      `}>
        <FiLock size={iconSizes[size]} />
        Protected
      </span>
    );
  }

  if (access === 'PRIVATE') {
    return (
      <span className={`
        inline-flex items-center gap-1 
        bg-red-100 text-red-800 
        font-medium rounded-full
        ${sizeClasses[size]}
        ${className}
      `}>
        <FiEyeOff size={iconSizes[size]} />
        Private
      </span>
    );
  }

  return null;
};

export default ProductAccessIndicator;
