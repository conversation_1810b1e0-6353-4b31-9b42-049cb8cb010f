"use client";

import { FiShield, FiUsers, FiTrendingUp, FiPackage, FiHeart, FiGlobe } from "react-icons/fi";
import { GiMedal, GiPlantRoots } from "react-icons/gi";
import { BiSolidCertification } from "react-icons/bi";
import Image from "next/image";

const WhyChooseUs = () => {
  const features = [
    {
      icon: GiPlantRoots,
      title: "100% Natural & Organic",
      description: "All our products are made with certified organic ingredients, ensuring purity and safety for your skin.",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: BiSolidCertification,
      title: "Premium Quality Standards",
      description: "GMP, ISO, and USDA certified facilities guarantee the highest quality in every product we manufacture.",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: FiPackage,
      title: "Bulk & Wholesale Solutions",
      description: "From individual purchases to large wholesale orders, we cater to businesses of all sizes worldwide.",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: FiUsers,
      title: "Expert Manufacturing Team",
      description: "Our experienced team provides contract manufacturing, private labeling, and custom formulation services.",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    },
    {
      icon: FiHeart,
      title: "Cruelty-Free & Ethical",
      description: "We're committed to ethical practices with cruelty-free products and sustainable manufacturing processes.",
      color: "text-pink-600",
      bgColor: "bg-pink-50"
    },
    {
      icon: FiGlobe,
      title: "Global Reach",
      description: "Serving customers in over 50 countries with reliable shipping and exceptional customer support.",
      color: "text-teal-600",
      bgColor: "bg-teal-50"
    }
  ];

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Content */}
          <div>
            <div className="mb-8">
              <span className="text-main-color font-semibold text-sm uppercase tracking-wider">
                Why Choose CocoJojo
              </span>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mt-2 mb-6">
                Your Trusted Partner in Beauty & Cosmetics
              </h2>
              <p className="text-gray-600 text-lg leading-relaxed">
                With over 15 years of experience in the cosmetics industry, CocoJojo has become 
                a leading provider of premium organic beauty products and comprehensive manufacturing services.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid sm:grid-cols-2 gap-6">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div
                    key={index}
                    className="group p-6 rounded-xl bg-white shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1"
                  >
                    <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className={`${feature.color} text-xl`} />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right Side - Image */}
          <div className="relative">
            <div className="relative h-96 md:h-[500px] lg:h-[600px] rounded-2xl overflow-hidden shadow-2xl">
              <Image
                src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80"
                alt="Premium cosmetic products and manufacturing"
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>

            {/* Floating Stats Card */}
            <div className="absolute -bottom-8 -left-8 bg-white rounded-xl shadow-xl p-6 border border-gray-100">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-main-color rounded-full flex items-center justify-center">
                  <GiMedal className="text-white text-xl" />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">15+</div>
                  <div className="text-gray-600 text-sm">Years of Excellence</div>
                </div>
              </div>
            </div>

            {/* Floating Quality Badge */}
            <div className="absolute -top-8 -right-8 bg-main-color text-white rounded-xl shadow-xl p-4 text-center">
              <div className="text-lg font-bold">100%</div>
              <div className="text-xs">Quality Guaranteed</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WhyChooseUs;
