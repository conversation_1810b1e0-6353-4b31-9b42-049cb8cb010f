"use client";

import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";

interface UseProtectedProductProps {
  productSlug: string;
  access: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
  productName: string;
}

interface UseProtectedProductReturn {
  isPasswordModalOpen: boolean;
  isLoading: boolean;
  error: string | null;
  openPasswordModal: () => void;
  closePasswordModal: () => void;
  handlePasswordSubmit: (password: string) => Promise<void>;
  handleProductClick: (e?: React.MouseEvent) => void;
}

export const useProtectedProduct = ({
  productSlug,
  access,
  productName
}: UseProtectedProductProps): UseProtectedProductReturn => {
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const openPasswordModal = useCallback(() => {
    setIsPasswordModalOpen(true);
    setError(null);
  }, []);

  const closePasswordModal = useCallback(() => {
    setIsPasswordModalOpen(false);
    setError(null);
    setIsLoading(false);
  }, []);

  const handlePasswordSubmit = useCallback(async (password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Try to access the product with the password
      const response = await fetch(`/api/shop/products/slug/${productSlug}?password=${encodeURIComponent(password)}`);
      
      if (response.ok) {
        // Password is correct, navigate to the product page
        closePasswordModal();
        router.push(`/product/${productSlug}?password=${encodeURIComponent(password)}`);
      } else {
        const errorData = await response.json();
        if (response.status === 404) {
          if (errorData.message?.includes('Invalid password')) {
            setError('Invalid password. Please try again.');
          } else if (errorData.message?.includes('protected')) {
            setError('This product requires a password to access.');
          } else {
            setError('Product not found or access denied.');
          }
        } else {
          setError('An error occurred while verifying the password.');
        }
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setError('An error occurred while verifying the password.');
    } finally {
      setIsLoading(false);
    }
  }, [productSlug, router, closePasswordModal]);

  const handleProductClick = useCallback((e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
    }

    if (access === 'PRIVATE') {
      // Private products should not be accessible
      return;
    }

    if (access === 'PROTECTED') {
      // Show password modal for protected products
      openPasswordModal();
    } else {
      // Public products can be accessed directly
      router.push(`/product/${productSlug}`);
    }
  }, [access, productSlug, router, openPasswordModal]);

  return {
    isPasswordModalOpen,
    isLoading,
    error,
    openPasswordModal,
    closePasswordModal,
    handlePasswordSubmit,
    handleProductClick
  };
};
