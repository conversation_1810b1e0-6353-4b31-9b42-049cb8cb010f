"use client";

import { useState } from 'react';
import { FiImage, FiUpload, FiFolder, FiGrid, FiList } from 'react-icons/fi';
import ImageUpload, { UploadedImage } from '@/components/admin/common/ImageUpload';

interface UploadedImageWithType extends UploadedImage {
  type: string;
  uploadedAt: Date;
}

const ImageManagementPage = () => {
  const [uploadedImages, setUploadedImages] = useState<UploadedImageWithType[]>([]);
  const [selectedType, setSelectedType] = useState<string>('product-image');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const uploadTypes = [
    { value: 'product-image', label: 'Product Images', folder: 'products' },
    { value: 'variant-image', label: 'Variant Images', folder: 'variants' },
    { value: 'category-image', label: 'Category Images', folder: 'categories' },
    { value: 'blog-image', label: 'Blog Images', folder: 'blogs' },
  ];

  const handleUpload = (images: UploadedImage[]) => {
    const newImages: UploadedImageWithType[] = images.map(img => ({
      ...img,
      type: selectedType,
      uploadedAt: new Date()
    }));

    setUploadedImages(prev => [...newImages, ...prev]);
    setSuccess(`Successfully uploaded ${images.length} image(s)`);
    setError(null);
    
    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setSuccess(null);
  };

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setSuccess('URL copied to clipboard!');
      setTimeout(() => setSuccess(null), 2000);
    } catch (err) {
      setError('Failed to copy URL');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const filteredImages = uploadedImages.filter(img => 
    selectedType === 'all' || img.type === selectedType
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Image Management</h1>
        <p className="text-gray-600">Upload and manage images for your products, variants, categories, and blog posts.</p>
      </div>

      {/* Upload Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
        <div className="flex items-center gap-3 mb-4">
          <FiUpload className="text-main-color" size={24} />
          <h2 className="text-xl font-semibold text-gray-900">Upload Images</h2>
        </div>

        {/* Upload Type Selector */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Type
          </label>
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
          >
            {uploadTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 text-green-700 rounded-md">
            {success}
          </div>
        )}

        {/* Upload Component */}
        <ImageUpload
          onUpload={handleUpload}
          onError={handleError}
          multiple={true}
          maxFiles={10}
          uploadType={selectedType as any}
          className="max-w-2xl"
        />
      </div>

      {/* Images Gallery */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <FiImage className="text-main-color" size={24} />
            <h2 className="text-xl font-semibold text-gray-900">
              Uploaded Images ({filteredImages.length})
            </h2>
          </div>

          <div className="flex items-center gap-4">
            {/* Filter */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            >
              <option value="all">All Types</option>
              {uploadTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border border-gray-300 rounded-md">
              <button
                type="button"
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-main-color text-white' : 'text-gray-600 hover:bg-gray-50'}`}
              >
                <FiGrid size={16} />
              </button>
              <button
                type="button"
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-main-color text-white' : 'text-gray-600 hover:bg-gray-50'}`}
              >
                <FiList size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Images Display */}
        {filteredImages.length > 0 ? (
          viewMode === 'grid' ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {filteredImages.map((image, index) => (
                <div key={index} className="group relative bg-gray-50 rounded-lg overflow-hidden border border-gray-200">
                  <div className="aspect-square relative">
                    <img
                      src={image.url}
                      alt={image.originalName}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-2">
                    <p className="text-xs text-gray-600 truncate" title={image.originalName}>
                      {image.originalName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(image.size)}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => copyToClipboard(image.url)}
                    className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100"
                  >
                    <span className="bg-white text-gray-900 px-3 py-1 rounded text-sm font-medium">
                      Copy URL
                    </span>
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredImages.map((image, index) => (
                <div key={index} className="flex items-center gap-4 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="w-12 h-12 rounded overflow-hidden flex-shrink-0">
                    <img
                      src={image.url}
                      alt={image.originalName}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">{image.originalName}</p>
                    <p className="text-sm text-gray-500">
                      {formatFileSize(image.size)} • {image.type.replace('-', ' ')} • {image.uploadedAt.toLocaleDateString()}
                    </p>
                  </div>
                  <button
                    type="button"
                    onClick={() => copyToClipboard(image.url)}
                    className="px-3 py-1 text-sm bg-main-color text-white rounded hover:bg-main-color/90 transition-colors"
                  >
                    Copy URL
                  </button>
                </div>
              ))}
            </div>
          )
        ) : (
          <div className="text-center py-12 text-gray-500">
            <FiFolder size={48} className="mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">No images found</p>
            <p>Upload some images to get started</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ImageManagementPage;
