import { NextRequest, NextResponse } from 'next/server';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: ContactFormData = await request.json();
    
    // Validate required fields
    const { name, email, subject, message } = body;
    
    if (!name || !email || !subject || !message) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Sanitize inputs to prevent XSS
    const sanitizedData = {
      name: name.trim().substring(0, 100),
      email: email.trim().toLowerCase().substring(0, 100),
      subject: subject.trim().substring(0, 200),
      message: message.trim().substring(0, 2000)
    };

    // Create email content
    const emailContent = {
      to: '<EMAIL>',
      from: sanitizedData.email,
      subject: `Contact Form: ${sanitizedData.subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #0C7D8F; margin: 0; font-size: 24px;">New Contact Form Submission</h1>
              <div style="width: 50px; height: 3px; background-color: #C7D494; margin: 10px auto;"></div>
            </div>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Contact Information</h2>
              <p style="margin: 5px 0; color: #555;"><strong>Name:</strong> ${sanitizedData.name}</p>
              <p style="margin: 5px 0; color: #555;"><strong>Email:</strong> ${sanitizedData.email}</p>
              <p style="margin: 5px 0; color: #555;"><strong>Subject:</strong> ${sanitizedData.subject}</p>
            </div>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
              <h2 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Message</h2>
              <div style="background-color: white; padding: 15px; border-radius: 5px; border-left: 4px solid #0C7D8F;">
                <p style="margin: 0; color: #555; line-height: 1.6; white-space: pre-wrap;">${sanitizedData.message}</p>
              </div>
            </div>
            
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
              <p style="margin: 0; color: #888; font-size: 12px;">
                This message was sent from the COCOJOJO website contact form.<br>
                Submitted on: ${new Date().toLocaleString('en-US', { 
                  timeZone: 'America/Los_Angeles',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  timeZoneName: 'short'
                })}
              </p>
            </div>
          </div>
        </div>
      `,
      text: `
New Contact Form Submission

Contact Information:
Name: ${sanitizedData.name}
Email: ${sanitizedData.email}
Subject: ${sanitizedData.subject}

Message:
${sanitizedData.message}

Submitted on: ${new Date().toLocaleString('en-US', { 
  timeZone: 'America/Los_Angeles',
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  hour: '2-digit',
  minute: '2-digit',
  timeZoneName: 'short'
})}
      `
    };

    // For now, we'll log the email content since we don't have email service configured
    // In production, you would integrate with services like:
    // - SendGrid
    // - Nodemailer with SMTP
    // - AWS SES
    // - Resend
    // - etc.
    
    console.log('Contact form submission:', {
      timestamp: new Date().toISOString(),
      data: sanitizedData,
      emailContent: emailContent
    });

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Auto-reply email content for the customer
    const autoReplyContent = {
      to: sanitizedData.email,
      from: '<EMAIL>',
      subject: 'Thank you for contacting COCOJOJO',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #0C7D8F; margin: 0; font-size: 24px;">Thank You for Contacting Us!</h1>
              <div style="width: 50px; height: 3px; background-color: #C7D494; margin: 10px auto;"></div>
            </div>
            
            <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">Dear ${sanitizedData.name},</p>
            
            <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
              Thank you for reaching out to COCOJOJO! We have received your message regarding "${sanitizedData.subject}" and appreciate you taking the time to contact us.
            </p>
            
            <p style="color: #555; line-height: 1.6; margin-bottom: 20px;">
              Our team will review your inquiry and respond within 24 hours. If you have any urgent questions, please don't hesitate to call us at <strong>(+1) ************</strong>.
            </p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #0C7D8F; margin: 0 0 10px 0;">Your Message Summary:</h3>
              <p style="margin: 5px 0; color: #555;"><strong>Subject:</strong> ${sanitizedData.subject}</p>
              <p style="margin: 5px 0; color: #555;"><strong>Submitted:</strong> ${new Date().toLocaleString()}</p>
            </div>
            
            <p style="color: #555; line-height: 1.6; margin-bottom: 30px;">
              Best regards,<br>
              <strong>The COCOJOJO Team</strong>
            </p>
            
            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
              <p style="margin: 0; color: #888; font-size: 12px;">
                COCOJOJO - Natural Beauty Excellence<br>
                3109 S Main St, Santa Ana, CA 92707<br>
                Phone: (+1) ************ | Email: <EMAIL>
              </p>
            </div>
          </div>
        </div>
      `
    };

    console.log('Auto-reply email:', autoReplyContent);

    return NextResponse.json(
      { 
        success: true, 
        message: 'Message sent successfully! We will get back to you within 24 hours.' 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    );
  }
}
