"use client";

import { useEffect, useState } from "react";
import { useMainCategoryStore } from "@/hooks/useMainCategoryStore";
import { FiEdit2, FiTrash2, FiPlus, FiEye, FiImage } from "react-icons/fi";
import { MainCategory } from "@/services/api";
import LoadingSpinner from "@/components/LoadingSpinner";
import Image from "next/image";

interface MainCategoryListProps {
  onEdit: (mainCategory: MainCategory) => void;
  onAddCategories: (mainCategory: MainCategory) => void;
  onViewCategories: (mainCategoryId: number) => void;
}

const MainCategoryList = ({ onEdit, onAddCategories, onViewCategories }: MainCategoryListProps) => {
  const { mainCategories, isLoading, error, fetchMainCategories, deleteMainCategory, setSelectedMainCategory } = useMainCategoryStore();
  const [isDeleting, setIsDeleting] = useState<number | null>(null);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  useEffect(() => {
    fetchMainCategories();
  }, [fetchMainCategories]);

  const handleEdit = (mainCategory: MainCategory) => {
    setSelectedMainCategory(mainCategory);
    onEdit(mainCategory);
  };

  const handleAddCategories = (mainCategory: MainCategory) => {
    setSelectedMainCategory(mainCategory);
    onAddCategories(mainCategory);
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm("Are you sure you want to delete this main category? This will remove the association with categories but won't delete the categories themselves.")) {
      return;
    }

    setIsDeleting(id);
    setDeleteError(null);

    try {
      await deleteMainCategory(id);
    } catch (err) {
      setDeleteError(err instanceof Error ? err.message : "Failed to delete main category");
    } finally {
      setIsDeleting(null);
    }
  };

  if (isLoading && mainCategories.length === 0) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        <p>Error loading main categories: {error}</p>
        <button
          onClick={() => fetchMainCategories()}
          className="mt-2 bg-red-100 hover:bg-red-200 text-red-700 px-3 py-1 rounded text-sm"
        >
          Try Again
        </button>
      </div>
    );
  }

  if ((mainCategories || []).length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">No main categories found.</p>
      </div>
    );
  }

  return (
    <div>
      {deleteError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          <p>{deleteError}</p>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Slug
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Categories
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {(mainCategories || []).map((mainCategory) => (
              <tr key={mainCategory.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {mainCategory.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {mainCategory.imageUrl ? (
                    <div className="relative h-10 w-10 rounded-md overflow-hidden">
                      <Image
                        src={mainCategory.imageUrl}
                        alt={mainCategory.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <FiImage className="h-10 w-10 text-gray-300 p-2 border border-gray-200 rounded-md" />
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => onViewCategories(mainCategory.id!)}
                    className="text-gray-900 hover:text-blue-600 font-medium hover:underline text-left"
                    title="View Categories in this Main Category"
                  >
                    {mainCategory.name}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {mainCategory.slug}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {mainCategory.categories?.length || 0}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => handleEdit(mainCategory)}
                      className="text-indigo-600 hover:text-indigo-900"
                      title="Edit Main Category"
                    >
                      <FiEdit2 size={18} />
                    </button>
                    <button
                      onClick={() => handleAddCategories(mainCategory)}
                      className="text-green-600 hover:text-green-900"
                      title="Add Categories"
                    >
                      <FiPlus size={18} />
                    </button>
                    <button
                      onClick={() => onViewCategories(mainCategory.id!)}
                      className="text-blue-600 hover:text-blue-900"
                      title="View Categories"
                    >
                      <FiEye size={18} />
                    </button>
                    <button
                      onClick={() => handleDelete(mainCategory.id!)}
                      className="text-red-600 hover:text-red-900"
                      disabled={isDeleting === mainCategory.id}
                      title="Delete Main Category"
                    >
                      {isDeleting === mainCategory.id ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <FiTrash2 size={18} />
                      )}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MainCategoryList;
