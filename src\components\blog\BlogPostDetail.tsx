"use client";

import Link from 'next/link';
import Image from 'next/image';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, FiCalendar, FiArrowLeft, FiShare2, FiBookmark } from 'react-icons/fi';
import { BlogPost } from '@/types/blog';
import BlogContent from './BlogContent';
import RelatedPosts from './RelatedPosts';

interface BlogPostDetailProps {
  post: BlogPost;
}

export default function BlogPostDetail({ post }: BlogPostDetailProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Back Navigation */}
      <div className="bg-white border-b">
        <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-4">
          <Link 
            href="/blog"
            className="inline-flex items-center gap-2 text-main-color hover:text-main-color/80 transition-colors font-medium"
          >
            <FiArrowLeft className="w-4 h-4" />
            Back to Blog
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative h-[500px] overflow-hidden">
        <Image
          src={post.blogImage}
          alt={post.title}
          fill
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
        
        {/* Content Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-8 md:p-12 lg:p-16">
          <div className="max-w-4xl mx-auto text-white">
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* Title */}
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              {post.title}
            </h1>

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-6 text-white/90">
              <div className="flex items-center gap-2">
                {post.author.avatar && (
                  <div className="relative w-8 h-8 rounded-full overflow-hidden">
                    <Image
                      src={post.author.avatar}
                      alt={post.author.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <span className="font-medium">{post.author.name}</span>
              </div>
              <div className="flex items-center gap-1">
                <FiCalendar className="w-4 h-4" />
                <span>{formatDate(post.createdDate)}</span>
              </div>
              <div className="flex items-center gap-1">
                <FiClock className="w-4 h-4" />
                <span>{post.readTime} min read</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Article Content */}
            <div className="lg:col-span-3">
              {/* Article Actions */}
              <div className="flex items-center justify-between mb-8 p-4 bg-white rounded-lg shadow-sm border">
                <div className="flex items-center gap-4">
                  <button
                    onClick={handleShare}
                    className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-main-color transition-colors"
                  >
                    <FiShare2 className="w-4 h-4" />
                    Share
                  </button>
                  <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-main-color transition-colors">
                    <FiBookmark className="w-4 h-4" />
                    Save
                  </button>
                </div>
                <div className="text-sm text-gray-500">
                  Published {formatDate(post.createdDate)}
                </div>
              </div>

              {/* Article Body */}
              <article className="prose prose-lg max-w-none">
                <div className="bg-white rounded-lg shadow-sm border p-8">
                  <BlogContent content={post.content} />
                </div>
              </article>

              {/* Author Bio */}
              <div className="mt-12 p-6 bg-white rounded-lg shadow-sm border">
                <h3 className="text-xl font-bold text-gray-900 mb-4">About the Author</h3>
                <div className="flex items-start gap-4">
                  {post.author.avatar && (
                    <div className="relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
                      <Image
                        src={post.author.avatar}
                        alt={post.author.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">{post.author.name}</h4>
                    {post.author.bio && (
                      <p className="text-gray-600">{post.author.bio}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                {/* Table of Contents */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">In this article</h3>
                  <nav className="space-y-2">
                    {post.content
                      .filter(block => block.type === 'heading')
                      .map((heading, index) => (
                        <a
                          key={index}
                          href={`#heading-${index}`}
                          className="block text-sm text-gray-600 hover:text-main-color transition-colors py-1"
                          style={{ paddingLeft: `${(heading.level || 1) - 1}rem` }}
                        >
                          {heading.content}
                        </a>
                      ))}
                  </nav>
                </div>

                {/* Tags */}
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag) => (
                      <Link
                        key={tag}
                        href={`/blog?tags=${tag}`}
                        className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-main-color hover:text-white transition-colors"
                      >
                        {tag}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Related Posts */}
          <div className="mt-16">
            <RelatedPosts currentPost={post} />
          </div>
        </div>
      </div>
    </div>
  );
}
