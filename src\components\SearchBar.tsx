"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState, useEffect, useCallback } from "react";
import { FiSearch, FiX } from "react-icons/fi";
import { useDebounce } from "@/hooks/useDebounce";

const SearchBar = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState<any[]>([]);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  const handleSearch = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      router.push(`/list?query=${encodeURIComponent(searchTerm.trim())}`);
      setShowResults(false);
    }
  };

  const fetchSearchResults = useCallback(async (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/shop/products/search?query=${encodeURIComponent(query)}&limit=5`);
      if (!response.ok) throw new Error('Search failed');
      const data = await response.json();
      setResults(data);
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (debouncedSearchTerm) {
      fetchSearchResults(debouncedSearchTerm);
    } else {
      setResults([]);
    }
  }, [debouncedSearchTerm, fetchSearchResults]);

  return (
    <div className="relative w-full max-w-md">
      <form
        className="relative flex items-center bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 focus-within:ring-2 focus-within:ring-main-color/20 focus-within:border-main-color"
        onSubmit={handleSearch}
      >
        <div className="flex items-center pl-4">
          <FiSearch className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setShowResults(true);
          }}
          onFocus={() => setShowResults(true)}
          onBlur={() => setTimeout(() => setShowResults(false), 200)}
          placeholder="Search products..."
          className="flex-1 px-4 py-3 bg-transparent outline-none text-gray-900 placeholder-gray-500"
        />
        {searchTerm && (
          <button
            type="button"
            onClick={() => {
              setSearchTerm("");
              setResults([]);
              setShowResults(false);
            }}
            className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX className="w-4 h-4" />
          </button>
        )}
        <button
          type="submit"
          className="px-4 py-3 text-main-color hover:text-main-color/80 transition-colors"
        >
          <FiSearch className="w-5 h-5" />
        </button>
      </form>

      {/* Search Results Dropdown */}
      {showResults && (searchTerm || isLoading) && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-100 z-[200] max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-main-color mx-auto mb-2"></div>
              <p className="text-gray-500 text-sm">Searching...</p>
            </div>
          ) : results.length > 0 ? (
            <div className="py-2">
              <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                Products ({results.length})
              </div>
              {results.map((product) => (
                <div
                  key={product.id}
                  className="p-4 hover:bg-gray-50 cursor-pointer flex items-center gap-4 transition-colors duration-200"
                  onClick={() => {
                    // Handle protected products
                    if (product.access === 'PRIVATE') {
                      // Don't navigate to private products
                      return;
                    } else if (product.access === 'PROTECTED') {
                      // For protected products, navigate to product page which will handle password prompt
                      router.push(`/product/${product.slug}`);
                    } else {
                      // Public products can be accessed directly
                      router.push(`/product/${product.slug}`);
                    }
                    setShowResults(false);
                    setSearchTerm("");
                  }}
                >
                  {product.imageUrl ? (
                    <div className="w-12 h-12 relative flex-shrink-0">
                      <Image
                        src={product.imageUrl}
                        alt={product.name}
                        fill
                        className="object-cover rounded-lg"
                        sizes="48px"
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <FiSearch className="w-5 h-5 text-gray-400" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                    <div className="flex items-center gap-2 mt-1">
                      {product.salePrice ? (
                        <>
                          <span className="text-main-color font-semibold">${product.salePrice}</span>
                          <span className="text-gray-400 line-through text-sm">${product.price}</span>
                        </>
                      ) : (
                        <span className="text-main-color font-semibold">${product.price}</span>
                      )}
                    </div>
                  </div>
                  <FiSearch className="w-4 h-4 text-gray-400" />
                </div>
              ))}
              <div className="px-4 py-3 border-t border-gray-100">
                <button
                  onClick={() => {
                    router.push(`/shop?search=${encodeURIComponent(searchTerm)}`);
                    setShowResults(false);
                  }}
                  className="text-main-color hover:text-main-color/80 text-sm font-medium transition-colors"
                >
                  View all results for "{searchTerm}"
                </button>
              </div>
            </div>
          ) : searchTerm ? (
            <div className="p-6 text-center">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <FiSearch className="w-6 h-6 text-gray-400" />
              </div>
              <p className="text-gray-500 mb-2">No products found</p>
              <p className="text-gray-400 text-sm">Try searching with different keywords</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default SearchBar;