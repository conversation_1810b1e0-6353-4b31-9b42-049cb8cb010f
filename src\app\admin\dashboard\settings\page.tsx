"use client";

import { useState } from "react";
import TaxSettings from "@/components/admin/settings/TaxSettings";
import AdminManagement from "@/components/admin/settings/AdminManagement";

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState<"general" | "tax" | "admin">("general");

  return (
    <div>
      <h1 className="text-2xl font-bold mb-8">Settings</h1>
      
      <div className="mb-6 border-b border-gray-200">
        <div className="flex flex-wrap -mb-px">
          <button
            onClick={() => setActiveTab("general")}
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              activeTab === "general"
                ? "border-main-color text-main-color"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            General
          </button>
          <button
            onClick={() => setActiveTab("tax")}
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              activeTab === "tax"
                ? "border-main-color text-main-color"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Tax Settings
          </button>
          <button
            onClick={() => setActiveTab("admin")}
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              activeTab === "admin"
                ? "border-main-color text-main-color"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Admin Management
          </button>
        </div>
      </div>

      {/* General Settings Tab */}
      {activeTab === "general" && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-semibold mb-4">General Settings</h2>
          <p className="text-gray-500">General settings will be available soon.</p>
        </div>
      )}

      {/* Tax Settings Tab */}
      {activeTab === "tax" && (
        <TaxSettings />
      )}

      {/* Admin Management Tab */}
      {activeTab === "admin" && (
        <AdminManagement />
      )}
    </div>
  );
};

export default SettingsPage;
