"use client";

import { useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FiCheckCircle, FiShoppingBag, FiHome } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";

const CheckoutSuccessPage = () => {
  const router = useRouter();
  
  // Ensure cart is cleared
  useEffect(() => {
    useCartStore.getState().clearCart();
  }, []);

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16">
      <div className="max-w-2xl mx-auto text-center">
        <div className="flex justify-center mb-6">
          <FiCheckCircle size={64} className="text-green-500" />
        </div>
        
        <h1 className="text-3xl font-medium mb-4">Thank You for Your Order!</h1>
        <p className="text-gray-600 mb-8">
          Your order has been received and is now being processed. You will receive an email confirmation shortly.
        </p>
        
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-medium mb-4">Order Information</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div>
              <p className="text-gray-500 text-sm">Order Number:</p>
              <p className="font-medium">ORD-{Math.floor(Math.random() * 10000).toString().padStart(4, '0')}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">Date:</p>
              <p className="font-medium">{new Date().toLocaleDateString()}</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">Payment Method:</p>
              <p className="font-medium">Demo Payment</p>
            </div>
            <div>
              <p className="text-gray-500 text-sm">Shipping Method:</p>
              <p className="font-medium">Standard Shipping</p>
            </div>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link 
            href="/"
            className="flex items-center justify-center gap-2 px-6 py-3 bg-main-color text-white rounded-md hover:bg-main-color/90"
          >
            <FiHome size={18} />
            Return to Home
          </Link>
          <Link 
            href="/shop"
            className="flex items-center justify-center gap-2 px-6 py-3 border border-main-color text-main-color rounded-md hover:bg-main-color/5"
          >
            <FiShoppingBag size={18} />
            Continue Shopping
          </Link>
        </div>
      </div>
    </div>
  );
};

export default CheckoutSuccessPage;
