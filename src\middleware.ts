import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // We can't check localStorage in middleware, so we'll rely on the client-side checks
  // in the components themselves. This middleware is now just a fallback.

  // For security, we'll keep the middleware but make it less restrictive
  // The actual authentication check will happen in the component

  return NextResponse.next()
}

export const config = {
  matcher: ['/admin/dashboard/:path*', '/profile/:path*', '/profile']
}