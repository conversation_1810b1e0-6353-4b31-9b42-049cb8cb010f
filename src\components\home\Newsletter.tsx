"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>heck, FiArrowRight } from "react-icons/fi";

const Newsletter = () => {
  const [email, setEmail] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubscribed(true);
      setIsLoading(false);
      setEmail("");
    }, 1500);
  };

  if (isSubscribed) {
    return (
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-6">
              <FiCheck className="text-main-color text-2xl" />
            </div>
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Thank You for Subscribing!
            </h3>
            <p className="text-white/90 text-lg">
              You'll receive our latest updates, exclusive offers, and beauty tips directly in your inbox.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12">
          {/* Header */}
          <div className="mb-8">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiMail className="text-white text-2xl" />
            </div>
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Stay Updated with CocoJojo
            </h3>
            <p className="text-white/90 text-lg leading-relaxed max-w-2xl mx-auto">
              Get exclusive access to new product launches, special discounts, industry insights, 
              and beauty tips from our experts.
            </p>
          </div>

          {/* Benefits */}
          <div className="grid sm:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-white text-xl">🎁</span>
              </div>
              <h4 className="text-white font-semibold mb-2">Exclusive Offers</h4>
              <p className="text-white/80 text-sm">Special discounts and early access to sales</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-white text-xl">🚀</span>
              </div>
              <h4 className="text-white font-semibold mb-2">New Launches</h4>
              <p className="text-white/80 text-sm">Be first to know about new products</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-white text-xl">💡</span>
              </div>
              <h4 className="text-white font-semibold mb-2">Expert Tips</h4>
              <p className="text-white/80 text-sm">Beauty and business insights from pros</p>
            </div>
          </div>

          {/* Newsletter Form */}
          <form onSubmit={handleSubmit} className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full px-6 py-4 rounded-lg bg-white/90 backdrop-blur-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white focus:bg-white transition-all duration-300"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isLoading || !email}
                className="px-8 py-4 bg-white text-main-color rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 flex items-center justify-center gap-2 group"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-main-color"></div>
                ) : (
                  <>
                    <span>Subscribe</span>
                    <FiArrowRight className="group-hover:translate-x-1 transition-transform duration-300" />
                  </>
                )}
              </button>
            </div>
          </form>

          {/* Privacy Note */}
          <p className="text-white/70 text-xs mt-4">
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Newsletter;
