import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Product Sections API - Base URL:', API_BASE_URL);
console.log('Product Sections API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// GET handler for fetching all product sections
export async function GET(request: NextRequest) {
  try {
    console.log('Fetching all product sections...');
    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      console.error(`Failed to fetch product sections: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch product sections' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Successfully fetched product sections');
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /product-sections):', error);
    return NextResponse.json(
      { error: 'Failed to fetch product sections' },
      { status: 500 }
    );
  }
}

// POST handler for creating a new product section
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Creating new product section:', body);

    const response = await fetch(`${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error(`Failed to create product section: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to create product section' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Successfully created product section:', data);
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /product-sections):', error);
    return NextResponse.json(
      { error: 'Failed to create product section' },
      { status: 500 }
    );
  }
}
