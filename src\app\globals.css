@tailwind base;
@tailwind components;
@tailwind utilities;

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide{
  scrollbar-width: none;
}

/* Blog content styling */
.blog-content {
  line-height: 1.7;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  scroll-margin-top: 2rem;
}

.blog-content blockquote {
  position: relative;
}

.blog-content blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: -0.5rem;
  font-size: 3rem;
  color: #0C7D8F;
  opacity: 0.3;
  font-family: serif;
}

/* Product description HTML styling */
.product-description {
  overflow: hidden;
}

.product-description strong {
  font-weight: 600;
}

.product-description p {
  margin-bottom: 0.5rem;
}

.product-description ol,
.product-description ul {
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.product-description ol {
  list-style-type: decimal;
}

.product-description ul {
  list-style-type: disc;
}

.product-description li {
  margin-bottom: 0.25rem;
}

.product-description em {
  font-style: italic;
}

/* Ensure line-clamp works with HTML content */
.line-clamp-2.product-description {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}