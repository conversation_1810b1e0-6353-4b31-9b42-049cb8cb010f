import { create } from "zustand";
import { ProductSection, ProductSectionItem, Product, productSectionApi } from "@/services/api";

interface ProductSectionState {
  sections: ProductSection[];
  isLoading: boolean;
  error: string | null;
  selectedSection: ProductSection | null;

  // Actions
  fetchSections: () => Promise<ProductSection[]>;
  fetchSectionById: (id: number) => Promise<ProductSection | null>;
  fetchSectionByPosition: (position: number) => Promise<ProductSection | null>;
  createSection: (section: { name: string; position: number; productIds?: number[] }) => Promise<ProductSection | null>;
  updateSection: (id: number, section: { name?: string; position?: number }) => Promise<ProductSection | null>;
  deleteSection: (id: number) => Promise<boolean>;
  addProductToSection: (sectionId: number, productId: number, position: number) => Promise<ProductSection | null>;
  removeProductFromSection: (sectionId: number, itemId: number) => Promise<ProductSection | null>;
  setSelectedSection: (section: ProductSection | null) => void;
  clearError: () => void;
}

export const useProductSectionStore = create<ProductSectionState>((set, get) => ({
  sections: [],
  isLoading: false,
  error: null,
  selectedSection: null,

  clearError: () => set({ error: null }),

  fetchSections: async () => {
    set({ isLoading: true, error: null });
    try {
      const sections = await productSectionApi.getAll();
      set({ sections, isLoading: false });
      return sections;
    } catch (error) {
      console.error('Store error - fetchSections:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch product sections',
        isLoading: false,
        sections: [] // Reset sections on error
      });
      return [];
    }
  },

  fetchSectionById: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const section = await productSectionApi.getById(id);
      set({ isLoading: false });
      return section;
    } catch (error) {
      console.error(`Store error - fetchSectionById(${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to fetch product section with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  fetchSectionByPosition: async (position: number) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Store - Fetching product section with position ${position}...`);
      const section = await productSectionApi.getByPosition(position);
      console.log(`Store - Successfully fetched product section with position ${position}:`, section);
      set({ isLoading: false });
      return section;
    } catch (error) {
      console.error(`Store error - fetchSectionByPosition(${position}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to fetch product section with position ${position}`,
        isLoading: false
      });
      return null;
    }
  },

  createSection: async (section: { name: string; position: number; productIds?: number[] }) => {
    set({ isLoading: true, error: null });
    try {
      const newSection = await productSectionApi.create(section);
      set(state => ({
        sections: [...state.sections, newSection],
        isLoading: false
      }));
      return newSection;
    } catch (error) {
      console.error('Store error - createSection:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create product section',
        isLoading: false
      });
      return null;
    }
  },

  updateSection: async (id: number, section: { name?: string; position?: number }) => {
    set({ isLoading: true, error: null });
    try {
      const updatedSection = await productSectionApi.update(id, section);
      set(state => ({
        sections: state.sections.map(s => s.id === id ? updatedSection : s),
        isLoading: false
      }));
      return updatedSection;
    } catch (error) {
      console.error(`Store error - updateSection(${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update product section with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  deleteSection: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await productSectionApi.delete(id);
      set(state => ({
        sections: state.sections.filter(s => s.id !== id),
        isLoading: false
      }));
      return true;
    } catch (error) {
      console.error(`Store error - deleteSection(${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete product section with ID ${id}`,
        isLoading: false
      });
      return false;
    }
  },

  addProductToSection: async (sectionId: number, productId: number, position: number) => {
    set({ isLoading: true, error: null });
    try {
      const updatedSection = await productSectionApi.addProduct(sectionId, productId, position);

      // Update the section in the store
      set(state => ({
        sections: state.sections.map(s => s.id === sectionId ? updatedSection : s),
        isLoading: false
      }));

      return updatedSection;
    } catch (error) {
      console.error(`Store error - addProductToSection(${sectionId}, ${productId}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to add product ID ${productId} to section ID ${sectionId}`,
        isLoading: false
      });
      return null;
    }
  },

  removeProductFromSection: async (sectionId: number, itemId: number) => {
    set({ isLoading: true, error: null });
    try {
      const updatedSection = await productSectionApi.removeProduct(sectionId, itemId);

      // Update the section in the store
      set(state => ({
        sections: state.sections.map(s => s.id === sectionId ? updatedSection : s),
        isLoading: false
      }));

      return updatedSection;
    } catch (error) {
      console.error(`Store error - removeProductFromSection(${sectionId}, ${itemId}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to remove item ID ${itemId} from section ID ${sectionId}`,
        isLoading: false
      });
      return null;
    }
  },

  setSelectedSection: (section: ProductSection | null) => {
    set({ selectedSection: section });
  }
}));

// Export alias for backward compatibility
export const useSectionStore = useProductSectionStore;
