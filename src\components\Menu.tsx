"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useAuthStore } from "@/hooks/useAuthStore";
import { useRouter } from "next/navigation";
import { useCartStore } from "@/hooks/useCartStore";
import { FiX, FiHome, FiShoppingBag, FiUser, FiLogOut, FiLogIn, FiFileText, FiInfo, FiMail } from "react-icons/fi";

const Menu = () => {
  const [open, setOpen] = useState(false);
  const { isAuthenticated, logout, user } = useAuthStore();
  const { counter = 0 } = useCartStore();
  const router = useRouter();

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [open]);

  const handleLogout = async () => {
    await logout();
    setOpen(false);
    router.push("/login");
  };

  const handleLinkClick = () => {
    setOpen(false);
  };

  const menuItems = [
    { href: "/", label: "Homepage", icon: FiHome },
    { href: "/shop", label: "Shop", icon: FiShoppingBag },
    { href: "/wholesale", label: "Wholesale", icon: FiShoppingBag },
    { href: "/blog", label: "Blog", icon: FiFileText },
    { href: "/about", label: "About", icon: FiInfo },
    { href: "/contact", label: "Contact", icon: FiMail },
  ];

  return (
    <>
      {/* Menu Toggle Button */}
      <button
        onClick={() => setOpen(!open)}
        className="relative z-[10000] p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
        aria-label="Toggle menu"
      >
        {open ? (
          <FiX size={24} className="text-gray-900" />
        ) : (
          <Image
            src="/menu.png"
            alt="Menu"
            width={24}
            height={24}
            className="cursor-pointer"
          />
        )}
      </button>

      {/* Mobile Menu Overlay and Panel */}
      {open && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9998] transition-opacity duration-300"
            onClick={() => setOpen(false)}
          />

          {/* Mobile Menu Panel */}
          <div
            className="fixed top-0 right-0 h-screen w-80 max-w-[85vw] bg-white shadow-2xl z-[9999] overflow-y-auto border-l border-gray-200"
            style={{
              backgroundColor: '#ffffff',
              transform: 'translateX(0)',
              transition: 'transform 0.3s ease-out'
            }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-white">
              <div className="flex items-center gap-3">
                <Image src="/images/cocojojo.png" alt="CocoJojo" width={40} height={40} />
                <span className="text-xl font-bold text-gray-900">CocoJojo</span>
              </div>
              <button
                onClick={() => setOpen(false)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                aria-label="Close menu"
              >
                <FiX size={20} />
              </button>
            </div>

          {/* User Info */}
          {isAuthenticated() && user && (
            <div className="p-6 bg-gray-50 border-b border-gray-100">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-main-color rounded-full flex items-center justify-center">
                  <FiUser className="text-white" size={20} />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">{user.firstName} {user.lastName}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Links */}
          <nav className="flex-1 py-6 bg-white">
            <ul className="space-y-2 px-4">
              {menuItems.map((item) => {
                const Icon = item.icon;
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      onClick={handleLinkClick}
                      className="flex items-center gap-4 px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-main-color transition-all duration-200 group"
                    >
                      <Icon size={20} className="group-hover:scale-110 transition-transform duration-200" />
                      <span className="font-medium">{item.label}</span>
                    </Link>
                  </li>
                );
              })}

              {/* Cart Link */}
              <li>
                <Link
                  href="/cart"
                  onClick={handleLinkClick}
                  className="flex items-center gap-4 px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-main-color transition-all duration-200 group"
                >
                  <div className="relative">
                    <FiShoppingBag size={20} className="group-hover:scale-110 transition-transform duration-200" />
                    {counter > 0 && (
                      <span className="absolute -top-2 -right-2 bg-main-color text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {counter}
                      </span>
                    )}
                  </div>
                  <span className="font-medium">Cart</span>
                  {counter > 0 && (
                    <span className="ml-auto bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs">
                      {counter} {counter === 1 ? 'item' : 'items'}
                    </span>
                  )}
                </Link>
              </li>
            </ul>
          </nav>

          {/* Auth Section */}
          <div className="border-t border-gray-100 p-4 bg-white">
            {isAuthenticated() ? (
              <div className="space-y-2">
                <Link
                  href="/profile"
                  onClick={handleLinkClick}
                  className="flex items-center gap-4 px-4 py-3 rounded-lg text-gray-700 hover:bg-gray-100 hover:text-main-color transition-all duration-200 group w-full"
                >
                  <FiUser size={20} className="group-hover:scale-110 transition-transform duration-200" />
                  <span className="font-medium">Profile</span>
                </Link>
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-4 px-4 py-3 rounded-lg text-red-600 hover:bg-red-50 transition-all duration-200 group w-full"
                >
                  <FiLogOut size={20} className="group-hover:scale-110 transition-transform duration-200" />
                  <span className="font-medium">Logout</span>
                </button>
              </div>
            ) : (
              <Link
                href="/login"
                onClick={handleLinkClick}
                className="flex items-center justify-center gap-3 bg-main-color text-white px-6 py-3 rounded-lg hover:bg-main-color/90 transition-all duration-200 font-medium"
              >
                <FiLogIn size={20} />
                <span>Login</span>
              </Link>
            )}
          </div>
          </div>
        </>
      )}
    </>
  );
};

export default Menu;