"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  FiHome,
  FiBox,
  FiUsers,
  FiSettings,
  FiShoppingBag,
  FiLogOut,
  FiLayers,
  FiImage
} from "react-icons/fi";
import { useAuthStore } from "@/hooks/useAuthStore";

const AdminSidebar = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const menuItems = [
    { icon: FiHome, label: "Dashboard", href: "/admin/dashboard" },
    { icon: FiBox, label: "Store", href: "/admin/dashboard/store" },
    { icon: FiImage, label: "Images", href: "/admin/dashboard/images" },
    { icon: FiLayers, label: "Section Management", href: "/admin/dashboard/sections" },
    { icon: FiShoppingBag, label: "Orders", href: "/admin/dashboard/orders" },
    { icon: FiUsers, label: "Users", href: "/admin/dashboard/users" },
    { icon: FiSettings, label: "Settings", href: "/admin/dashboard/settings" },
  ];

  return (
    <div className="w-64 bg-white h-screen fixed left-0 top-0 shadow-lg">
      <div className="p-4 border-b">
        <h1 className="text-2xl font-bold text-main-color">Admin Panel</h1>
      </div>
      <nav className="p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={`flex items-center space-x-2 p-2 rounded-lg hover:bg-main-color/10 ${
                    pathname === item.href ? "bg-main-color/10 text-main-color" : "text-gray-700"
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
      <div className="absolute bottom-0 w-full p-4 border-t">
        <div className="mb-2 px-2">
          {user && (
            <div className="text-sm text-gray-600">
              <div className="font-medium text-gray-800">{user.name || user.email}</div>
              <div className="text-xs">{user.isSuperAdmin ? 'Super Admin' : 'Admin'}</div>
            </div>
          )}
        </div>
        <button
          onClick={async () => {
            await logout();
            router.push('/admin');
          }}
          className="flex items-center space-x-2 text-gray-700 hover:text-main-color w-full p-2 rounded-lg hover:bg-main-color/10"
        >
          <FiLogOut className="w-5 h-5" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  );
};

export default AdminSidebar;