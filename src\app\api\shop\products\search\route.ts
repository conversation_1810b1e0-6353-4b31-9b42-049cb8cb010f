import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchParams = url.searchParams;
    const name = searchParams.get('query') || '';
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';
    // const categoryId = searchParams.get('categoryId');

    // Construct the backend URL with search parameters
    let backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products/search?query=${encodeURIComponent(name)}&page=${page}&limit=${limit}`;
    
    // // Add categoryId if it exists
    // if (categoryId) {
    //   backendUrl += `&categoryId=${encodeURIComponent(categoryId)}`;
    // }

    console.log(`Search API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to search products' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Search API error:', error);
    return NextResponse.json(
      { error: 'Failed to search products' },
      { status: 500 }
    );
  }
} 