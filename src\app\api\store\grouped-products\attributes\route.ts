import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

export async function GET() {
  try {
    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/grouped-products/attributes`);
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /grouped-products/attributes):', error);
    return NextResponse.json(
      { error: 'Failed to fetch attributes' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/grouped-products/attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /grouped-products/attributes):', error);
    return NextResponse.json(
      { error: 'Failed to create attribute' },
      { status: 500 }
    );
  }
}