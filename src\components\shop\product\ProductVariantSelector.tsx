"use client";

import { useState } from 'react';
import { FiCheck } from 'react-icons/fi';

interface AttributeValue {
  id: number;
  value: string;
}

interface Attribute {
  id: number;
  name: string;
  values: AttributeValue[];
}

interface ProductAttribute {
  attribute: Attribute;
  values: AttributeValue[];
}

interface ProductVariantSelectorProps {
  productAttributes: ProductAttribute[];
  selectedAttributes: Record<string, AttributeValue>;
  onAttributeChange: (attributeId: number, value: AttributeValue) => void;
}

const ProductVariantSelector = ({
  productAttributes,
  selectedAttributes,
  onAttributeChange,
}: ProductVariantSelectorProps) => {
  // Check if a value is selected for a specific attribute
  const isValueSelected = (attributeId: number, valueId: number) => {
    return selectedAttributes[attributeId]?.id === valueId;
  };

  // Determine if a value appears to be a color
  const isColorValue = (value: string) => {
    // Check if the value is a valid CSS color
    const isHexColor = /^#([0-9A-F]{3}){1,2}$/i.test(value);
    const isRgbColor = /^rgb\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)$/i.test(value);
    const isRgbaColor = /^rgba\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)$/i.test(value);
    const isNamedColor = /^(red|blue|green|yellow|purple|pink|orange|brown|black|white|gray|grey)$/i.test(value);
    
    return isHexColor || isRgbColor || isRgbaColor || isNamedColor;
  };

  // Get a valid CSS color from a value
  const getColorFromValue = (value: string) => {
    if (/^#([0-9A-F]{3}){1,2}$/i.test(value)) {
      return value; // Already a valid hex color
    }
    
    // For named colors, return as is
    const namedColors = ['red', 'blue', 'green', 'yellow', 'purple', 'pink', 'orange', 'brown', 'black', 'white', 'gray', 'grey'];
    if (namedColors.includes(value.toLowerCase())) {
      return value.toLowerCase();
    }
    
    // Default fallback color
    return '#cccccc';
  };

  // Render a color swatch for color values
  const renderColorSwatch = (value: string, isSelected: boolean) => {
    const color = getColorFromValue(value);
    
    return (
      <div 
        className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
          isSelected ? 'border-main-color' : 'border-gray-300'
        }`}
      >
        <div 
          className="w-8 h-8 rounded-full"
          style={{ backgroundColor: color }}
        >
          {isSelected && (
            <div className="w-full h-full flex items-center justify-center">
              <FiCheck 
                size={16} 
                className={`${color.toLowerCase() === 'white' || color.toLowerCase() === '#ffffff' ? 'text-gray-800' : 'text-white'}`} 
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="mb-8 space-y-6">
      {productAttributes.map((productAttr) => (
        <div key={productAttr.attribute.id} className="space-y-3">
          <h3 className="font-medium text-gray-800">
            {productAttr.attribute.name}
          </h3>
          
          <div className="flex flex-wrap gap-3">
            {productAttr.values.map((value) => {
              const isSelected = isValueSelected(productAttr.attribute.id, value.id);
              const isColor = isColorValue(value.value);
              
              return (
                <button
                  key={value.id}
                  onClick={() => onAttributeChange(productAttr.attribute.id, value)}
                  className={`
                    transition-all duration-200 
                    ${isColor 
                      ? '' 
                      : `px-4 py-2 rounded-lg border ${
                          isSelected 
                            ? 'bg-main-color text-white border-main-color' 
                            : 'bg-white text-gray-800 border-gray-300 hover:border-main-color'
                        }`
                    }
                  `}
                  aria-label={`Select ${productAttr.attribute.name}: ${value.value}`}
                >
                  {isColor 
                    ? renderColorSwatch(value.value, isSelected)
                    : value.value
                  }
                </button>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductVariantSelector;
