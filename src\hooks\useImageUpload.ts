import { useState } from 'react';

export interface UploadedImage {
  filename: string;
  originalName: string;
  size: number;
  mimetype: string;
  url: string;
  path: string;
}

export interface UseImageUploadOptions {
  uploadType?: 'product-image' | 'variant-image' | 'category-image' | 'blog-image' | 'multiple-images';
  subfolder?: string;
  maxFileSize?: number; // in bytes
  allowedTypes?: string[];
}

export const useImageUpload = (options: UseImageUploadOptions = {}) => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    uploadType = 'product-image',
    subfolder,
    maxFileSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  } = options;

  const validateFile = (file: File): string | null => {
    if (!allowedTypes.includes(file.type)) {
      return `Invalid file type: ${file.name}. Allowed types: ${allowedTypes.join(', ')}`;
    }

    if (file.size > maxFileSize) {
      return `File too large: ${file.name}. Maximum size: ${Math.round(maxFileSize / 1024 / 1024)}MB`;
    }

    return null;
  };

  const uploadSingle = async (file: File): Promise<UploadedImage | null> => {
    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return null;
    }

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      if (subfolder) {
        formData.append('subfolder', subfolder);
      }

      const endpoint = uploadType === 'multiple-images' ? 'product-image' : uploadType;
      const response = await fetch(`/api/uploads/${endpoint}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      return null;
    } finally {
      setUploading(false);
    }
  };

  const uploadMultiple = async (files: File[]): Promise<UploadedImage[]> => {
    // Validate all files first
    for (const file of files) {
      const validationError = validateFile(file);
      if (validationError) {
        setError(validationError);
        return [];
      }
    }

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });
      if (subfolder) {
        formData.append('subfolder', subfolder);
      }

      const response = await fetch('/api/uploads/multiple-images', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      return [];
    } finally {
      setUploading(false);
    }
  };

  const uploadFromUrl = async (url: string): Promise<UploadedImage | null> => {
    setUploading(true);
    setError(null);

    try {
      // Fetch the image from the URL
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch image from URL');
      }

      const blob = await response.blob();
      const filename = url.split('/').pop() || 'image';
      const file = new File([blob], filename, { type: blob.type });

      return await uploadSingle(file);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload from URL';
      setError(errorMessage);
      return null;
    }
  };

  const clearError = () => setError(null);

  return {
    uploading,
    error,
    uploadSingle,
    uploadMultiple,
    uploadFromUrl,
    clearError,
    validateFile
  };
};
