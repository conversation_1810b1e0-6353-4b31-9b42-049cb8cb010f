export interface ShopProduct {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice?: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  access: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  categories?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  stockQuantity?: number | null;
  stockStatus?: string;
  productType?: string;
  discountPercent?: number;
  isOnSale?: boolean;
}
