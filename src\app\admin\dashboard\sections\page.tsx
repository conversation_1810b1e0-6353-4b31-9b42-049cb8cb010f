"use client";

import { useState } from "react";
import ProductSectionList from "@/components/admin/sections/ProductSectionList";
import ProductSectionForm from "@/components/admin/sections/ProductSectionForm";
import ProductSectionItemsView from "@/components/admin/sections/ProductSectionItemsView";
import { ProductSection } from "@/services/api";

type FormMode = "add" | "edit" | null;
type ActiveView = "sections" | "sectionItems";

const ProductSectionsPage = () => {
  const [formMode, setFormMode] = useState<FormMode>(null);
  const [selectedSectionId, setSelectedSectionId] = useState<number | null>(null);
  const [activeView, setActiveView] = useState<ActiveView>("sections");

  const handleAddNew = () => {
    setFormMode("add");
  };

  const handleCloseForm = () => {
    setFormMode(null);
  };

  const handleEdit = (section: ProductSection) => {
    setSelectedSectionId(section.id!);
    setFormMode("edit");
  };

  const handleViewSectionItems = (sectionId: number) => {
    setSelectedSectionId(sectionId);
    setActiveView("sectionItems");
  };

  const handleBackToSections = () => {
    setSelectedSectionId(null);
    setActiveView("sections");
  };

  return (
    <div className="min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Product Section Management</h1>
        {activeView === "sections" && !formMode && (
          <button
            onClick={handleAddNew}
            className="bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition"
          >
            Add New Product Section
          </button>
        )}
      </div>

      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        {activeView === "sections" && (
          <>
            {formMode ? (
              <ProductSectionForm
                mode={formMode}
                sectionId={selectedSectionId}
                onClose={handleCloseForm}
              />
            ) : (
              <ProductSectionList
                onEdit={handleEdit}
                onViewItems={handleViewSectionItems}
              />
            )}
          </>
        )}
        {activeView === "sectionItems" && selectedSectionId && (
          <ProductSectionItemsView
            sectionId={selectedSectionId}
            onBack={handleBackToSections}
          />
        )}
      </div>
    </div>
  );
};

export default ProductSectionsPage;
