"use client";

import Link from 'next/link';
import Image from 'next/image';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, FiCalendar, FiArrowRight } from 'react-icons/fi';
import { BlogPost } from '@/types/blog';

interface BlogCardProps {
  post: BlogPost;
  featured?: boolean;
  layout?: 'grid' | 'list';
}

export default function BlogCard({ post, featured = false, layout = 'grid' }: BlogCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (layout === 'list') {
    return (
      <article className="group bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all duration-300 overflow-hidden">
        <div className="flex flex-col md:flex-row">
          {/* Image */}
          <div className="relative h-64 md:h-auto md:w-1/3 overflow-hidden">
            <Link href={`/blog/${post.slug}`}>
              <Image
                src={post.blogImage}
                alt={post.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-500"
              />
            </Link>
            {featured && (
              <div className="absolute top-4 left-4 bg-main-color text-white px-3 py-1 rounded-full text-sm font-medium">
                Featured
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 p-6 flex flex-col justify-between">
            <div>
              {/* Tags */}
              <div className="flex flex-wrap gap-2 mb-3">
                {post.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-main-color hover:text-white transition-colors cursor-pointer"
                  >
                    {tag}
                  </span>
                ))}
                {post.tags.length > 3 && (
                  <span className="text-gray-500 text-sm">+{post.tags.length - 3} more</span>
                )}
              </div>

              {/* Title */}
              <Link href={`/blog/${post.slug}`}>
                <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-3 group-hover:text-main-color transition-colors line-clamp-2">
                  {post.title}
                </h2>
              </Link>

              {/* Excerpt */}
              <p className="text-gray-600 mb-4 line-clamp-3">
                {post.excerpt}
              </p>
            </div>

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <FiUser className="w-4 h-4" />
                <span>{post.author.name}</span>
              </div>
              <div className="flex items-center gap-1">
                <FiCalendar className="w-4 h-4" />
                <span>{formatDate(post.createdDate)}</span>
              </div>
              <div className="flex items-center gap-1">
                <FiClock className="w-4 h-4" />
                <span>{post.readTime} min read</span>
              </div>
              <Link 
                href={`/blog/${post.slug}`}
                className="ml-auto flex items-center gap-1 text-main-color hover:text-main-color/80 transition-colors font-medium"
              >
                Read more
                <FiArrowRight className="w-4 h-4" />
              </Link>
            </div>
          </div>
        </div>
      </article>
    );
  }

  return (
    <article className={`group bg-white rounded-xl shadow-sm border hover:shadow-lg transition-all duration-300 overflow-hidden h-full flex flex-col ${
      featured ? 'ring-2 ring-main-color/20' : ''
    }`}>
      {/* Image */}
      <div className="relative h-64 overflow-hidden">
        <Link href={`/blog/${post.slug}`}>
          <Image
            src={post.blogImage}
            alt={post.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-500"
          />
        </Link>
        {featured && (
          <div className="absolute top-4 left-4 bg-main-color text-white px-3 py-1 rounded-full text-sm font-medium">
            Featured
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6 flex-1 flex flex-col">
        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-3">
          {post.tags.slice(0, 2).map((tag) => (
            <span
              key={tag}
              className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-main-color hover:text-white transition-colors cursor-pointer"
            >
              {tag}
            </span>
          ))}
          {post.tags.length > 2 && (
            <span className="text-gray-500 text-sm">+{post.tags.length - 2}</span>
          )}
        </div>

        {/* Title */}
        <Link href={`/blog/${post.slug}`}>
          <h2 className={`font-bold text-gray-900 mb-3 group-hover:text-main-color transition-colors line-clamp-2 ${
            featured ? 'text-xl md:text-2xl' : 'text-lg md:text-xl'
          }`}>
            {post.title}
          </h2>
        </Link>

        {/* Excerpt */}
        <p className="text-gray-600 mb-4 line-clamp-3 flex-1">
          {post.excerpt}
        </p>

        {/* Meta Info */}
        <div className="flex flex-wrap items-center gap-3 text-sm text-gray-500 mt-auto">
          <div className="flex items-center gap-1">
            <FiUser className="w-4 h-4" />
            <span>{post.author.name}</span>
          </div>
          <div className="flex items-center gap-1">
            <FiClock className="w-4 h-4" />
            <span>{post.readTime} min</span>
          </div>
          <div className="flex items-center gap-1 ml-auto">
            <FiCalendar className="w-4 h-4" />
            <span>{formatDate(post.createdDate)}</span>
          </div>
        </div>

        {/* Read More Link */}
        <Link 
          href={`/blog/${post.slug}`}
          className="mt-4 flex items-center gap-2 text-main-color hover:text-main-color/80 transition-colors font-medium group/link"
        >
          Read full article
          <FiArrowRight className="w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
        </Link>
      </div>
    </article>
  );
}
