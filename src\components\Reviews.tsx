import Image from "next/image";

// Dummy reviews data
const dummyReviews = {
  data: [
    {
      id: "1",
      customer: {
        display_name: "<PERSON>",
        avatar_url: "https://images.unsplash.com/photo-1599566150163-29194dcaad36?w=800&auto=format&fit=crop"
      },
      rating: 5,
      heading: "Amazing natural product",
      body: "I've been using this for a month now and I can really see the difference. My skin feels so much better!",
      media: [
        {
          id: "m1",
          url: "https://images.unsplash.com/photo-1556229010-6c3f2c9ca5f8?w=800&auto=format&fit=crop"
        }
      ]
    },
    {
      id: "2",
      customer: {
        display_name: "<PERSON>",
        avatar_url: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=800&auto=format&fit=crop"
      },
      rating: 4,
      heading: "Great quality",
      body: "The ingredients are all natural and the scent is lovely. Would recommend!",
      media: []
    },
    {
      id: "3",
      customer: {
        display_name: "<PERSON>",
        avatar_url: "https://images.unsplash.com/photo-1552374196-c4e7ffc6e126?w=800&auto=format&fit=crop"
      },
      rating: 5,
      heading: "Worth every penny",
      body: "Excellent product, fast shipping, and great customer service. Will buy again!",
      media: [
        {
          id: "m2",
          url: "https://images.unsplash.com/photo-1617897903246-719242758050?w=800&auto=format&fit=crop"
        }
      ]
    }
  ]
};

const Reviews = async ({ productId }: { productId: string }) => {
  // Using dummy data instead of API call
  const reviews = dummyReviews;

  return (
    <div className="flex flex-col gap-8">
      {reviews.data.map((review) => (
        <div className="flex flex-col gap-4" key={review.id}>
          {/* USER */}
          <div className="flex items-center gap-4 font-medium">
            <Image
              src={review.customer.avatar_url}
              alt=""
              width={32}
              height={32}
              className="rounded-full"
            />
            <span>{review.customer.display_name}</span>
          </div>
          {/* STARS */}
          <div className="flex gap-2">
            {Array.from({ length: review.rating }).map((_, index) => (
              <Image src="/star.png" alt="" key={index} width={16} height={16} />
            ))}
          </div>
          {/* DESC */}
          {review.heading && <p className="font-medium">{review.heading}</p>}
          {review.body && <p className="text-gray-600">{review.body}</p>}
          {/* MEDIA */}
          {review.media && review.media.length > 0 && (
            <div className="flex gap-2 flex-wrap">
              {review.media.map((media) => (
                <Image
                  src={media.url}
                  key={media.id}
                  alt=""
                  width={100}
                  height={50}
                  className="object-cover rounded-md"
                />
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Reviews;

