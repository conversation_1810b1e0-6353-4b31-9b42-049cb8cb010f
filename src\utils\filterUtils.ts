import { FilterOptions } from '@/components/shop/ProductFilters';

/**
 * Builds a query string from filter options
 */
export function buildFilterQuery(filters: FilterOptions, page: number = 1, limit: number = 12): string {
  const params = new URLSearchParams();

  // Always include pagination
  params.set('page', page.toString());
  params.set('limit', limit.toString());

  // Add filters
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        if (value.length > 0) {
          params.set(key, value.join(','));
        }
      } else {
        params.set(key, value.toString());
      }
    }
  });

  return params.toString();
}

/**
 * Parses URL search params into filter options
 */
export function parseFiltersFromUrl(searchParams: URLSearchParams): FilterOptions {
  const filters: FilterOptions = {};

  // Parse each filter type
  const minPrice = searchParams.get('minPrice');
  if (minPrice) filters.minPrice = Number(minPrice);

  const maxPrice = searchParams.get('maxPrice');
  if (maxPrice) filters.maxPrice = Number(maxPrice);



  const onSale = searchParams.get('onSale');
  if (onSale) filters.onSale = onSale === 'true';

  const inStock = searchParams.get('inStock');
  if (inStock) filters.inStock = inStock === 'true';



  const categoryIds = searchParams.get('categoryIds');
  if (categoryIds) filters.categoryIds = categoryIds.split(',').map(Number);

  const tagNames = searchParams.get('tagNames');
  if (tagNames) filters.tagNames = tagNames.split(',');

  const search = searchParams.get('search');
  if (search) filters.search = search;

  const sortBy = searchParams.get('sortBy');
  if (sortBy) filters.sortBy = sortBy;

  const minDiscountPercent = searchParams.get('minDiscountPercent');
  if (minDiscountPercent) filters.minDiscountPercent = Number(minDiscountPercent);



  return filters;
}

/**
 * Fetches filter metadata for a given endpoint
 */
export async function fetchFilterMetadata(endpoint: string): Promise<any> {
  try {
    const response = await fetch(`${endpoint}/filters`);
    if (response.ok) {
      return await response.json();
    }
  } catch (error) {
    console.error('Error fetching filter metadata:', error);
  }
  return null;
}

/**
 * Builds the appropriate API endpoint based on the page type
 */
export function buildApiEndpoint(
  pageType: 'shop' | 'mainCategory' | 'category',
  mainCategorySlug?: string,
  categorySlug?: string
): string {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
  
  switch (pageType) {
    case 'shop':
      return `${baseUrl}/api/shop/products`;
    case 'mainCategory':
      return `${baseUrl}/api/shop/${mainCategorySlug}`;
    case 'category':
      return `${baseUrl}/api/shop/${mainCategorySlug}/${categorySlug}`;
    default:
      return `${baseUrl}/api/shop/products`;
  }
}

/**
 * Builds the filter metadata endpoint
 */
export function buildFilterEndpoint(
  pageType: 'shop' | 'mainCategory' | 'category',
  mainCategorySlug?: string,
  categorySlug?: string
): string {
  const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';
  
  switch (pageType) {
    case 'shop':
      return `${baseUrl}/api/shop/products`;
    case 'mainCategory':
      return `${baseUrl}/api/shop/${mainCategorySlug}`;
    case 'category':
      return `${baseUrl}/api/shop/${mainCategorySlug}/${categorySlug}`;
    default:
      return `${baseUrl}/api/shop/products`;
  }
}

/**
 * Updates URL with current filters and pagination
 */
export function updateUrlWithFilters(
  router: any,
  basePath: string,
  filters: FilterOptions,
  page: number,
  limit: number
) {
  const queryString = buildFilterQuery(filters, page, limit);
  const url = queryString ? `${basePath}?${queryString}` : basePath;
  router.push(url, { scroll: false });
}
