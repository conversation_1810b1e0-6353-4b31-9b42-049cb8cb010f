import { Metadata } from "next";
import { notFound } from "next/navigation";
import ProductDetailPageClient from "@/components/shop/ProductDetailPageClient";
import { ensureValidSlug } from "@/utils/slugUtils";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  images?: string[];
  price: number;
  salePrice?: number;
  inStock: boolean;
  shortDescription?: string;
  description?: string;
  tags?: string[];
  variants?: any[];
  created_at: string;
  access?: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
}

interface ProductPageProps {
  params: {
    productSlug: string;
  };
}

// Server-side data fetching
async function getProductData(productSlug: string) {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    // Fetch product data
    const response = await fetch(`${baseUrl}/api/shop/products/slug/${productSlug}`, {
      next: { revalidate: 7200 } // 24 hours cache
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch product data for ${productSlug}: ${response.status} ${response.statusText}`);
      return null;
    }
    
    const data = await response.json();
    // The backend returns the product data directly
    return { data };
  } catch (error) {
    console.error('Error fetching product data:', error);
    return null;
  }
}

// Generate static params for all products
export async function generateStaticParams() {
  try {
    // For now, return empty array to avoid build issues
    // In production, you can enable this to pre-generate product pages
    return [];

    // Uncomment below for static generation:
    /*
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://cocojojo.com'
      : 'http://localhost:3000';

    const response = await fetch(`${baseUrl}/api/shop/products`);

    if (!response.ok) {
      console.error(`Failed to fetch products for static generation: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    const products = data.data || [];

    return products.map((product: Product) => ({
      productSlug: product.slug,
    }));
    */
  } catch (error) {
    console.error('Error generating static params for products:', error);
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const normalizedSlug = ensureValidSlug(params.productSlug);
  const data = await getProductData(normalizedSlug);
  
  if (!data) {
    return {
      title: 'Product Not Found | CocoJojo',
      description: 'The requested product could not be found.',
    };
  }

  const product = data.data || data;

  // Clean HTML from descriptions for meta tags
  const cleanDescription = (html: string) => {
    return html?.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim() || '';
  };

  const metaDescription = cleanDescription(product.shortDescription) ||
                         cleanDescription(product.description) ||
                         `Buy ${product.name} at CocoJojo - Premium natural skincare products`;

  // Get the best image
  const getProductImage = () => {
    if (product.images && product.images.length > 0) {
      return product.images[0].url;
    }
    return 'https://cocojojo.com/images/cocojojo-logo.png'; // Fallback image
  };

  const productImage = getProductImage();
  const currentPrice = product.salePrice || product.price;
  const originalPrice = product.salePrice ? product.price : null;
  const stockStatus = product.stockStatus === 'IN_STOCK' ? 'In Stock' : 'Out of Stock';

  // Generate keywords from product data
  const keywords = [
    product.name,
    'CocoJojo',
    'natural skincare',
    'organic beauty',
    'premium skincare',
    ...(product.categories?.map((cat: any) => cat.name) || []),
    ...(product.tags?.map((tag: any) => tag.name) || [])
  ].join(', ');

  // Create rich description with price and availability
  const richDescription = `${metaDescription.substring(0, 120)}... ${stockStatus} - $${currentPrice.toFixed(2)}${originalPrice ? ` (was $${originalPrice.toFixed(2)})` : ''}`;

  return {
    title: `${product.name} | CocoJojo - Premium Natural Skincare`,
    description: richDescription,
    keywords,
    authors: [{ name: 'CocoJojo' }],
    creator: 'CocoJojo',
    publisher: 'CocoJojo',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: `${product.name} | CocoJojo`,
      description: metaDescription,
      url: `${process.env.VERCEL_URL}/product/${product.slug}`,
      siteName: 'CocoJojo',
      images: [
        {
          url: productImage,
          width: 600,
          height: 600,
          alt: product.name,
        }
      ],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${product.name} | CocoJojo`,
      description: metaDescription,
      images: [productImage],
      creator: '@CocoJojo',
      site: '@CocoJojo',
    },
    alternates: {
      canonical: `${process.env.VERCEL_URL}/product/${product.slug}`,
    },
    other: {
      'product:price:amount': currentPrice.toString(),
      'product:price:currency': 'USD',
      'product:availability': product.stockStatus === 'IN_STOCK' ? 'in stock' : 'out of stock',
      'product:condition': 'new',
      'product:brand': 'CocoJojo',
      ...(originalPrice && {
        'product:original_price:amount': originalPrice.toString(),
        'product:original_price:currency': 'USD',
      }),
    },
  };
}

export default async function ProductPage({ params }: ProductPageProps) {
  // Normalize the slug parameter
  const normalizedSlug = ensureValidSlug(params.productSlug);
  
  // Fetch data server-side for SEO
  const data = await getProductData(normalizedSlug);
  
  if (!data) {
    notFound();
  }

  const product = data.data || data;

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": product.name,
            "description": product.shortDescription?.replace(/<[^>]*>/g, '') || product.description?.replace(/<[^>]*>/g, '') || product.name,
            "image": product.images && product.images.length > 0
              ? product.images.map((img: any) => img.url)
              : ['https://cocojojo.com/images/cocojojo-logo.png'],
            "sku": product.sku,
            "gtin": product.sku, // Using SKU as GTIN for now
            "category": product.categories && product.categories.length > 0 ? product.categories[0].name : "Beauty & Personal Care",
            "offers": {
              "@type": "Offer",
              "price": (product.salePrice || product.price).toFixed(2),
              "priceCurrency": "USD",
              "availability": product.stockStatus === 'IN_STOCK' ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
              "url": `https://cocojojo.com/product/${normalizedSlug}`,
              "seller": {
                "@type": "Organization",
                "name": "CocoJojo"
              },
              "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
              "itemCondition": "https://schema.org/NewCondition"
            },
            "brand": {
              "@type": "Brand",
              "name": "CocoJojo",
              "url": "https://cocojojo.com"
            },
            "manufacturer": {
              "@type": "Organization",
              "name": "CocoJojo",
              "url": "https://cocojojo.com"
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.5",
              "reviewCount": "24",
              "bestRating": "5",
              "worstRating": "1"
            },
            "review": [
              {
                "@type": "Review",
                "reviewRating": {
                  "@type": "Rating",
                  "ratingValue": "5",
                  "bestRating": "5"
                },
                "author": {
                  "@type": "Person",
                  "name": "Sarah Johnson"
                },
                "reviewBody": "Absolutely love this product! The quality is amazing and it works exactly as described."
              }
            ]
          })
        }}
      />

      {/* Breadcrumb JSON-LD */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
              {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "https://cocojojo.com"
              },
              {
                "@type": "ListItem",
                "position": 2,
                "name": "Shop",
                "item": "https://cocojojo.com/shop"
              },
              {
                "@type": "ListItem",
                "position": 3,
                "name": product.name,
                "item": `${process.env.VERCEL_URL}/product/${normalizedSlug}`
              }
            ]
          })
        }}
      />

      <ProductDetailPageClient
        initialProduct={product}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 600; // 24 hours
