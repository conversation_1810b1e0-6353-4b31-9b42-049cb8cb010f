import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Product Section by Position API - Base URL:', API_BASE_URL);
console.log('Product Section by Position API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// GET handler for fetching a product section by position
export async function GET(
  request: NextRequest,
  { params }: { params: { position: string } }
) {
  try {
    const position = params.position;

    console.log(`API Route: Fetching product section with position ${position}...`);
    const apiUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections/position/${position}`;
    console.log(`API Route: Full URL being called: ${apiUrl}`);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    console.log(`API Route: Response status for position ${position}: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      console.error(`API Route: Product section with position ${position} not found. Status: ${response.status}`);
      return NextResponse.json(
        { error: `Product section with position ${position} not found` },
        { status: response.status }
      );
    }

    // Clone the response to read it twice
    const responseClone = response.clone();
    const responseText = await responseClone.text();
    console.log(`API Route: Raw response for position ${position}:`, responseText);

    // Check if the response is empty
    if (!responseText || responseText.trim() === '') {
      console.error(`API Route: Empty response received for position ${position}`);
      return NextResponse.json(
        { error: `Empty response received from server for position ${position}` },
        { status: 500 }
      );
    }

    // Parse the JSON
    let data;
    try {
      data = JSON.parse(responseText);
      console.log(`API Route: Parsed data for position ${position}:`, data);
    } catch (jsonError) {
      console.error(`API Route: Invalid JSON in response for position ${position}:`, jsonError);
      return NextResponse.json(
        { error: `Invalid JSON response for position ${position}` },
        { status: 500 }
      );
    }

    // Check if the data has the expected structure
    if (!data || !data.name) {
      console.warn(`API Route: Response for position ${position} is missing expected fields`);
    }

    if (!data.items || !Array.isArray(data.items)) {
      console.warn(`API Route: Response for position ${position} has no items or items is not an array`);
    } else {
      console.log(`API Route: Section ${position} has ${data.items.length} items`);
    }

    console.log(`API Route: Successfully fetched product section with position ${position}`);
    return NextResponse.json(data);
  } catch (error) {
    console.error(`API Route: Error (GET /product-sections/position/${params.position}):`, error);
    return NextResponse.json(
      { error: `Failed to fetch product section with position ${params.position}` },
      { status: 500 }
    );
  }
}
