"use client";

import { useState, useEffect } from "react";
import { useTaxStore } from "@/hooks/useTaxStore";
import { FiAlertTriangle, FiSave } from "react-icons/fi";

const TaxSettings = () => {
  const { taxSettings, isLoading, error, fetchTaxSettings, updateTaxSettings, clearError } = useTaxStore();

  const [name, setName] = useState("");
  const [value, setValue] = useState("");
  const [formError, setFormError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchTaxSettings();
  }, [fetchTaxSettings]);

  useEffect(() => {
    if (taxSettings) {
      setName(taxSettings.name);
      setValue(taxSettings.value);
    }
  }, [taxSettings]);

  useEffect(() => {
    if (error) {
      setFormError(error);
    }
  }, [error]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setSuccessMessage(null);

    if (!name.trim()) {
      setFormError("Tax name is required");
      return;
    }

    if (!value.trim() || isNaN(parseFloat(value))) {
      setFormError("Tax value must be a valid number");
      return;
    }

    try {
      const result = await updateTaxSettings({ name, value });
      if (result) {
        // Fetch the latest tax settings after successful update
        await fetchTaxSettings();

        setSuccessMessage("Tax settings updated successfully");
        setTimeout(() => setSuccessMessage(null), 3000);
      }
    } catch (err) {
      setFormError(err instanceof Error ? err.message : "Failed to update tax settings");
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">Tax Settings</h2>

      {formError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center shadow-sm">
          <FiAlertTriangle className="mr-3 flex-shrink-0 text-red-500" size={20} />
          <span className="font-medium">{formError}</span>
        </div>
      )}

      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-md flex items-center shadow-sm">
          <span className="font-medium">{successMessage}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="taxName" className="block text-sm font-medium text-gray-700 mb-2">
            Tax Name
          </label>
          <input
            type="text"
            id="taxName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            placeholder="e.g., Standard VAT"
          />
        </div>

        <div>
          <label htmlFor="taxValue" className="block text-sm font-medium text-gray-700 mb-2">
            Tax Value (%)
          </label>
          <input
            type="text"
            id="taxValue"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            placeholder="e.g., 20.00"
          />
          <p className="text-xs text-gray-500 mt-1">Enter the tax percentage value (e.g., 20.00 for 20%)</p>
        </div>

        <div className="pt-4">
          <button
            type="submit"
            disabled={isLoading}
            className={`px-6 py-3 bg-main-color text-white rounded-md flex items-center hover:bg-opacity-90 transition-colors ${
              isLoading ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            <FiSave className="mr-2" />
            {isLoading ? "Saving..." : "Save Tax Settings"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default TaxSettings;
