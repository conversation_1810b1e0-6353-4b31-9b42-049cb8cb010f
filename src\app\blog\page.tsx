import type { Metadata } from 'next';
import BlogListing from '@/components/blog/BlogListing';

export const metadata: Metadata = {
  title: 'Blog - Beauty Tips & Skincare Insights',
  description: 'Discover expert beauty tips, skincare insights, and natural ingredient guides. Learn about organic skincare, beauty routines, and wellness from our experts.',
  keywords: 'beauty blog, skincare tips, organic beauty, natural skincare, beauty advice, skincare routine',
  openGraph: {
    title: 'CocoJojo Blog - Beauty Tips & Skincare Insights',
    description: 'Discover expert beauty tips, skincare insights, and natural ingredient guides from our beauty experts.',
    type: 'website',
    url: '/blog',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CocoJojo Blog - Beauty Tips & Skincare Insights',
    description: 'Discover expert beauty tips, skincare insights, and natural ingredient guides from our beauty experts.',
  },
};

interface BlogPageProps {
  searchParams: {
    query?: string;
    tags?: string;
    page?: string;
    sortBy?: string;
    sortOrder?: string;
  };
}

export default function BlogPage({ searchParams }: BlogPageProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <BlogListing searchParams={searchParams} />
    </div>
  );
}
