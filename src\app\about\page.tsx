import type { Metadata } from 'next';
import AboutUsContent from '@/components/about/AboutUsContent';

export const metadata: Metadata = {
  title: 'About Us - Our Story & Mission',
  description: 'Learn about COCOJOJO\'s journey, our passion for natural skincare, and our commitment to quality USDA-certified organic products since 2009.',
  keywords: 'about cocojojo, natural skincare company, USDA certified organic, beauty brand story, organic cosmetics',
  openGraph: {
    title: 'About COCOJOJO - Natural Beauty Excellence Since 2009',
    description: 'Discover our passion for natural skincare and our commitment to quality USDA-certified organic products.',
    type: 'website',
    url: '/about',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About COCOJOJO - Natural Beauty Excellence Since 2009',
    description: 'Discover our passion for natural skincare and our commitment to quality USDA-certified organic products.',
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <AboutUsContent />
    </div>
  );
}
