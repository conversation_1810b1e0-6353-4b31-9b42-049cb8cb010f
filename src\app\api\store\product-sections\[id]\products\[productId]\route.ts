import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Product Section Item API - Base URL:', API_BASE_URL);
console.log('Product Section Item API - Store Path Prefix:', API_STORE_PATH_PREFIX);

// DELETE handler for removing a product from a section
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; productId: string } }
) {
  try {
    const { id, productId } = params;
    console.log(`Removing product ID ${productId} from section ID ${id}...`);

    const response = await fetch(
      `${API_BASE_URL}${API_STORE_PATH_PREFIX}/product-sections/${id}/products/${productId}`,
      {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      console.error(`Failed to remove product ID ${productId} from section ID ${id}: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to remove product ID ${productId} from section ID ${id}` },
        { status: response.status }
      );
    }

    console.log(`Successfully removed product ID ${productId} from section ID ${id}`);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`API proxy error (DELETE /product-sections/${params.id}/products/${params.productId}):`, error);
    return NextResponse.json(
      { error: `Failed to remove product ID ${params.productId} from section ID ${params.id}` },
      { status: 500 }
    );
  }
}
