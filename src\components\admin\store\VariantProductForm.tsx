"use client";

import { useState, useEffect } from 'react';
import { FiX, FiPlus, FiAlertTriangle } from 'react-icons/fi';
import ImageManager from '../common/ImageManager';

// Define StockStatus enum locally since we can't import from @prisma/client
enum StockStatus {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  ON_BACKORDER = 'ON_BACKORDER'
}

interface VariantImage {
  id?: number;
  url: string;
  position: number;
  delete?: boolean;
}

interface VariantProductFormProps {
  productId: number;
  variantId: number;
  initialData?: {
    sku: string;
    price: number;
    salePrice?: number;
    saleStart?: string;
    saleEnd?: string;
    stockQuantity: number | null;
    stockStatus: StockStatus;
    // attributeValueIds: number[];
    images: VariantImage[];
  };
  onClose: () => void;
}

export default function VariantProductForm({ productId, variantId, initialData, onClose }: VariantProductFormProps) {
  const [formData, setFormData] = useState({
    sku: initialData?.sku || '',
    price: initialData?.price || '',
    salePrice: initialData?.salePrice || '',
    saleStart: initialData?.saleStart || '',
    saleEnd: initialData?.saleEnd || '',
    stockQuantity: initialData?.stockQuantity !== null ? (initialData?.stockQuantity || 1) : 1,
    stockStatus: initialData?.stockStatus || StockStatus.IN_STOCK,
 //   attributeValueIds: initialData?.attributeValueIds || [],
    images: initialData?.images || []
  });

  // Check if initial stockQuantity is null (unlimited)
  const [isUnlimitedQuantity, setIsUnlimitedQuantity] = useState(
    initialData?.stockQuantity === null
  );

  const [imageUrl, setImageUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle Escape key to close modal
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Utility function to clean price values
  const cleanPriceValue = (value: string): string => {
    // Remove all non-numeric characters except decimal point
    return value.replace(/[^0-9.]/g, '');
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === "price" || name === "salePrice") {
      // Clean price values to remove commas, dollar signs, and other non-numeric characters
      const cleanedValue = cleanPriceValue(value);
      setFormData(prev => ({
        ...prev,
        [name]: cleanedValue
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle unlimited quantity toggle
  const handleUnlimitedQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isUnlimited = e.target.checked;
    setIsUnlimitedQuantity(isUnlimited);

    if (isUnlimited) {
      // When unlimited is checked, set stockQuantity to 1 in the form (will be null in API)
      setFormData(prev => ({
        ...prev,
        stockQuantity: 1,
      }));
    } else {
      // When unlimited is unchecked, keep the current value or set to 1
      setFormData(prev => ({
        ...prev,
        stockQuantity: prev.stockQuantity || 1,
      }));
    }
  };

  const handleAddImage = () => {
    if (!imageUrl.trim()) {
      setError("Please enter an image URL");
      return;
    }

    const newImage: VariantImage = {
      url: imageUrl,
      position: formData.images.length
    };

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, newImage]
    }));
    setImageUrl('');
    setError(null);
  };

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...formData.images];
    const imageToRemove = updatedImages[index];

    if (imageToRemove.id) {
      // Mark existing image for deletion
      updatedImages[index] = { ...imageToRemove, delete: true };
    } else {
      // Remove new image
      updatedImages.splice(index, 1);
    }

    // Update positions
    const reorderedImages = updatedImages
      .filter(img => !img.delete)
      .map((img, idx) => ({
        ...img,
        position: idx
      }));

    setFormData(prev => ({
      ...prev,
      images: reorderedImages
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Clean up the form data to remove empty optional fields
      const cleanedFormData = {
        ...formData,
        // Keep price as string (backend expects string for decimal values)
        price: formData.price.toString(),
        // Only include salePrice if it's not empty, and keep as string
        salePrice: formData.salePrice && formData.salePrice.toString().trim() !== ''
          ? formData.salePrice.toString()
          : undefined,
        // Only include sale dates if they're not empty
        saleStart: formData.saleStart && formData.saleStart.trim() !== '' ? formData.saleStart : undefined,
        saleEnd: formData.saleEnd && formData.saleEnd.trim() !== '' ? formData.saleEnd : undefined,
        // Handle stockQuantity - null for unlimited, number for limited
        stockQuantity: isUnlimitedQuantity ? null : (
          typeof formData.stockQuantity === 'string' ? parseInt(formData.stockQuantity) || 1 : formData.stockQuantity
        ),
      };

      // Remove undefined values
      Object.keys(cleanedFormData).forEach(key => {
        if (cleanedFormData[key as keyof typeof cleanedFormData] === undefined) {
          delete cleanedFormData[key as keyof typeof cleanedFormData];
        }
      });

      console.log('Cleaned form data:', cleanedFormData);

      const response = await fetch(`/api/store/grouped-products/${productId}/variants/${variantId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanedFormData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update variant');
      }

      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while updating the variant');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => {
        // Close modal when clicking on backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="max-w-2xl w-full max-h-[90vh] bg-white rounded-lg shadow-lg flex flex-col">
        {/* Header - Fixed */}
        <div className="flex-shrink-0 p-6 border-b flex justify-between items-center">
          <h2 className="text-2xl font-semibold text-gray-800">
            Edit Variant
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center shadow-sm">
              <FiAlertTriangle className="mr-3 flex-shrink-0 text-red-500" size={20} />
              <span className="font-medium">{error}</span>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-2">
              SKU
            </label>
            <input
              type="text"
              id="sku"
              name="sku"
              value={formData.sku}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
              Price
            </label>
            <input
              type="number"
              id="price"
              name="price"
              value={formData.price}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-2">
              Sale Price
            </label>
            <input
              type="number"
              id="salePrice"
              name="salePrice"
              value={formData.salePrice}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>

          <div>
            <label htmlFor="stockQuantity" className="block text-sm font-medium text-gray-700 mb-2">
              Stock Quantity
            </label>
            <div className="space-y-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="unlimitedQuantity"
                  checked={isUnlimitedQuantity}
                  onChange={handleUnlimitedQuantityChange}
                  className="h-4 w-4 text-main-color focus:ring-main-color border-gray-300 rounded"
                />
                <label htmlFor="unlimitedQuantity" className="ml-2 text-sm text-gray-700">
                  Unlimited quantity
                </label>
              </div>
              <input
                type="number"
                id="stockQuantity"
                name="stockQuantity"
                value={formData.stockQuantity}
                onChange={handleChange}
                min="0"
                disabled={isUnlimitedQuantity}
                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color ${
                  isUnlimitedQuantity ? 'bg-gray-100 text-gray-500' : ''
                }`}
                placeholder={isUnlimitedQuantity ? 'Unlimited' : '0'}
              />
            </div>
          </div>

          <div>
            <label htmlFor="stockStatus" className="block text-sm font-medium text-gray-700 mb-2">
              Stock Status
            </label>
            <select
              id="stockStatus"
              name="stockStatus"
              value={formData.stockStatus}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            >
              {Object.values(StockStatus).map((status) => (
                <option key={status} value={status}>
                  {status.replace('_', ' ')}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Variant Images</h3>

          <ImageManager
            images={formData.images}
            onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
            uploadType="variant-image"
            maxImages={5}
            allowReorder={true}
          />

          {/* Manual URL input as fallback */}
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-2">
                Or add image URL manually
              </label>
              <input
                type="text"
                id="imageUrl"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <button
              type="button"
              onClick={handleAddImage}
              className="px-4 py-2 bg-main-color text-white rounded-md flex items-center"
            >
              <FiPlus className="mr-1" /> Add
            </button>
          </div>
        </div>

            <div className="flex justify-end space-x-4 pt-6 border-t">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`px-6 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors ${
                  isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}