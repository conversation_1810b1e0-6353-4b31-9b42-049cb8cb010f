"use client";

export default function BlogHero() {
  return (
    <div className="relative h-[400px] w-full overflow-hidden bg-gradient-to-br from-main-color to-main-color/80">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px)`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      {/* Content */}
      <div className="relative h-full flex items-center justify-center px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
        <div className="text-center text-white max-w-4xl">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 animate-fadeIn">
            Beauty & Wellness
            <span className="block text-light-green">Insights</span>
          </h1>

          <p className="text-lg md:text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed animate-fadeIn" style={{ animationDelay: '0.2s' }}>
            Discover expert tips, natural ingredient guides, and the latest trends in organic skincare.
            Your journey to radiant, healthy skin starts here.
          </p>

          <div className="flex flex-wrap justify-center gap-4 text-sm text-white/80 animate-fadeIn" style={{ animationDelay: '0.4s' }}>
            <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
              ✨ Expert Beauty Tips
            </span>
            <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
              🌿 Natural Ingredients
            </span>
            <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
              💚 Organic Skincare
            </span>
            <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
              🧴 Product Reviews
            </span>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-light-green/20 rounded-full blur-xl"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/5 rounded-full blur-lg"></div>
    </div>
  );
}
