import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// Log the API configuration to help with debugging
console.log('Category Products API - Base URL:', API_BASE_URL);
console.log('Category Products API - Path Prefix:', API_PATH_PREFIX);

// GET handler for fetching products by category ID with pagination
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get pagination parameters
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';
    const search = searchParams.get('search') || '';

    // Build query string for backend
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('limit', limit);

    // Add search parameter if provided
    if (search) {
      queryParams.append('search', search);
    }

    // Try both possible endpoint formats
    const backendUrl = `${API_BASE_URL}${API_PATH_PREFIX}/categories/${categoryId}/products?${queryParams.toString()}`;
    console.log(`Calling backend URL with pagination: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch products for category ID ${categoryId}` },
        { status: response.status }
      );
    }

    let data;
    try {
      data = await response.json();
    } catch (e) {
      console.error(`Error parsing JSON response for category ${categoryId}:`, e);
      // Return empty paginated response if we can't parse the response
      return NextResponse.json({
        data: [],
        pagination: {
          total: 0,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      });
    }

    console.log(`Category ${categoryId} API: Received ${Array.isArray(data) ? data.length : data?.data?.length || 0} products`);

    // Check if the response is already in paginated format
    if (data && data.data && Array.isArray(data.data) && data.pagination) {
      // Ensure pagination values are numbers
      data.pagination.page = parseInt(data.pagination.page);
      data.pagination.limit = parseInt(data.pagination.limit);
      data.pagination.total = parseInt(data.pagination.total);
      data.pagination.totalPages = parseInt(data.pagination.totalPages);
      return NextResponse.json(data);
    }

    // If the response is an array, convert it to paginated format
    if (Array.isArray(data)) {
      const paginatedResponse = {
        data: data,
        pagination: {
          total: data.length,
          page: parseInt(page),
          limit: parseInt(limit),
          totalPages: Math.ceil(data.length / parseInt(limit)),
          hasNext: false,
          hasPrev: false
        }
      };
      return NextResponse.json(paginatedResponse);
    }

    // If neither array nor paginated format, return empty response
    console.warn(`Unexpected response format for category ${categoryId}:`, typeof data);
    return NextResponse.json({
      data: [],
      pagination: {
        total: 0,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      }
    });
  } catch (error) {
    console.error(`API proxy error (GET /categories/${params.id}/products):`, error);
    return NextResponse.json(
      { error: `Failed to fetch products for category ID ${params.id}` },
      { status: 500 }
    );
  }
}

// POST handler for creating a new product in a specific category
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const categoryId = params.id;
    const body = await request.json();

    console.log(`API proxy - Creating product for category ${categoryId}:`, body);

    // Always remove the ProductAttribute field for the simple product endpoints
    // Grouped products should use their dedicated endpoints
    if ('ProductAttribute' in body) {
      delete body.ProductAttribute;
      console.log(`API proxy - Removed ProductAttribute field for product creation in category ${categoryId}`);
    }

    // Also remove variants field if present
    if ('variants' in body) {
      delete body.variants;
      console.log(`API proxy - Removed variants field for product creation in category ${categoryId}`);
    }

    const backendUrl = `${API_BASE_URL}${API_PATH_PREFIX}/categories/${categoryId}/products`;
    console.log(`API proxy - Creating product for category ${categoryId} at URL: ${backendUrl}`);
    console.log(`API proxy - Cleaned request body:`, body);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error response from backend: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to create product for category ID ${categoryId}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Created product for category ${categoryId}:`, data);

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error(`API proxy error (POST /categories/${params.id}/products):`, error);
    return NextResponse.json(
      { error: `Failed to create product for category ID ${params.id}` },
      { status: 500 }
    );
  }
}
