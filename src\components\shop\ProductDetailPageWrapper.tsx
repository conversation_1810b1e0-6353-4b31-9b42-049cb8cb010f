"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import ProductDetailPageClient from "@/components/shop/ProductDetailPageClient";
import PasswordPromptModal from "@/components/shop/PasswordPromptModal";

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  salePrice: number | null;
  stockQuantity: number;
  stockStatus: string;
  type: string;
  access: string;
  createdAt: string;
  updatedAt: string;
  images: any[];
  categories: any[];
  tags: any[];
  listings: any[];
  ProductAttribute?: any[];
  variants?: any[];
}

interface ProductDetailPageWrapperProps {
  initialProduct: Product;
  productSlug: string;
}

const ProductDetailPageWrapper = ({ 
  initialProduct, 
  productSlug 
}: ProductDetailPageWrapperProps) => {
  const [product, setProduct] = useState<Product | null>(initialProduct);
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check if product is protected and no password was provided
  useEffect(() => {
    if (initialProduct?.access === 'PROTECTED' && !searchParams?.get('password')) {
      setIsPasswordModalOpen(true);
    } else if (initialProduct?.access === 'PRIVATE') {
      // Redirect private products to shop
      router.push('/shop');
    }
  }, [initialProduct, searchParams, router]);

  const handlePasswordSubmit = async (password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Try to fetch the product with the password
      const response = await fetch(`/api/shop/products/slug/${productSlug}?password=${encodeURIComponent(password)}`);
      
      if (response.ok) {
        const data = await response.json();
        setProduct(data);
        setIsPasswordModalOpen(false);
        
        // Update URL with password parameter
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.set('password', password);
        router.replace(newUrl.pathname + newUrl.search);
      } else {
        const errorData = await response.json();
        if (response.status === 404) {
          if (errorData.message?.includes('Invalid password')) {
            setError('Invalid password. Please try again.');
          } else if (errorData.message?.includes('protected')) {
            setError('This product requires a password to access.');
          } else {
            setError('Product not found or access denied.');
          }
        } else {
          setError('An error occurred while verifying the password.');
        }
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setError('An error occurred while verifying the password.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordModalClose = () => {
    // If user closes modal for a protected product, redirect to shop
    if (initialProduct?.access === 'PROTECTED') {
      router.push('/shop');
    } else {
      setIsPasswordModalOpen(false);
    }
  };

  // Show loading state while checking password
  if (!product && isPasswordModalOpen) {
    return (
      <>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product...</p>
          </div>
        </div>
        
        <PasswordPromptModal
          isOpen={isPasswordModalOpen}
          onClose={handlePasswordModalClose}
          onSubmit={handlePasswordSubmit}
          productName={initialProduct?.name || 'Product'}
          isLoading={isLoading}
          error={error || undefined}
        />
      </>
    );
  }

  // Show error state if product couldn't be loaded
  if (!product) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Product Not Available</h1>
            <p className="text-gray-600 mb-6">
              This product is not available or you don't have permission to access it.
            </p>
            <button
              onClick={() => router.push('/shop')}
              className="px-6 py-3 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
            >
              Back to Shop
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <ProductDetailPageClient initialProduct={product} />

      <PasswordPromptModal
        isOpen={isPasswordModalOpen}
        onClose={handlePasswordModalClose}
        onSubmit={handlePasswordSubmit}
        productName={product.name}
        isLoading={isLoading}
        error={error || undefined}
      />
    </>
  );
};

export default ProductDetailPageWrapper;
