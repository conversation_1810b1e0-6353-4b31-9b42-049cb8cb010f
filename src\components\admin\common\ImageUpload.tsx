"use client";

import { useState, useRef } from 'react';
import { FiUpload, FiX, FiImage, FiLoader } from 'react-icons/fi';

export interface UploadedImage {
  filename: string;
  originalName: string;
  size: number;
  mimetype: string;
  url: string;
  path: string;
}

interface ImageUploadProps {
  onUpload: (images: UploadedImage[]) => void;
  onError?: (error: string) => void;
  multiple?: boolean;
  maxFiles?: number;
  uploadType?: 'product-image' | 'variant-image' | 'category-image' | 'blog-image' | 'multiple-images';
  subfolder?: string;
  existingImages?: string[];
  className?: string;
}

const ImageUpload = ({
  onUpload,
  onError,
  multiple = false,
  maxFiles = 10,
  uploadType = 'product-image',
  subfolder,
  existingImages = [],
  className = ''
}: ImageUploadProps) => {
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>(existingImages);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const fileArray = Array.from(files);
    
    // Validate file count
    if (multiple && fileArray.length > maxFiles) {
      onError?.(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Validate file types and sizes
    const validFiles = fileArray.filter(file => {
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!validTypes.includes(file.type)) {
        onError?.(`Invalid file type: ${file.name}. Allowed types: JPEG, PNG, GIF, WebP`);
        return false;
      }

      if (file.size > maxSize) {
        onError?.(`File too large: ${file.name}. Maximum size: 5MB`);
        return false;
      }

      return true;
    });

    if (validFiles.length === 0) return;

    uploadFiles(validFiles);
  };

  const uploadFiles = async (files: File[]) => {
    setUploading(true);

    try {
      if (multiple && files.length > 1) {
        // Upload multiple files
        const formData = new FormData();
        files.forEach(file => {
          formData.append('files', file);
        });
        if (subfolder) {
          formData.append('subfolder', subfolder);
        }

        const response = await fetch('/api/uploads/multiple-images', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Upload failed');
        }

        const result = await response.json();
        onUpload(result.data);
        
        // Update preview images
        const newUrls = result.data.map((img: UploadedImage) => img.url);
        setPreviewImages(prev => [...prev, ...newUrls]);
      } else {
        // Upload single file
        const formData = new FormData();
        formData.append('file', files[0]);
        if (subfolder) {
          formData.append('subfolder', subfolder);
        }

        const endpoint = uploadType === 'multiple-images' ? 'product-image' : uploadType;
        const response = await fetch(`/api/uploads/${endpoint}`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Upload failed');
        }

        const result = await response.json();
        onUpload([result.data]);
        
        // Update preview images
        setPreviewImages(prev => [...prev, result.data.url]);
      }
    } catch (error) {
      console.error('Upload error:', error);
      onError?.(error instanceof Error ? error.message : 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const removePreviewImage = (index: number) => {
    setPreviewImages(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
          dragOver
            ? 'border-main-color bg-main-color/5'
            : 'border-gray-300 hover:border-main-color hover:bg-gray-50'
        } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />

        {uploading ? (
          <div className="flex flex-col items-center">
            <FiLoader className="animate-spin text-main-color mb-2" size={32} />
            <p className="text-gray-600">Uploading...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <FiUpload className="text-gray-400 mb-2" size={32} />
            <p className="text-gray-600 mb-1">
              {multiple ? 'Drop images here or click to select' : 'Drop image here or click to select'}
            </p>
            <p className="text-sm text-gray-500">
              JPEG, PNG, GIF, WebP up to 5MB {multiple && `(max ${maxFiles} files)`}
            </p>
          </div>
        )}
      </div>

      {/* Preview Images */}
      {previewImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {previewImages.map((url, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                <img
                  src={url}
                  alt={`Preview ${index + 1}`}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  onError={(e) => {
                    console.error('Preview image failed to load:', url, e);
                  }}
                  onLoad={() => {
                    console.log('Preview image loaded successfully:', url);
                  }}
                />
              </div>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  removePreviewImage(index);
                }}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <FiX size={14} />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ImageUpload;
