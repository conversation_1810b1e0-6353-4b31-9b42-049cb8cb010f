"use client";

import { useState, useEffect } from "react";
import {
  ProductAttribute,
  AttributeValue,
  ProductVariant,
  StockStatus,
  ProductImage
} from "@/services/api";
import { FiAlertTriangle, FiEdit, FiSave, FiX, FiPlus } from "react-icons/fi";
import ImageManager from "../../common/ImageManager";

interface VariantsStepProps {
  attributes: ProductAttribute[];
  attributeValues: Record<number, AttributeValue[]>;
  variants: ProductVariant[];
  onVariantsChange: (variants: ProductVariant[]) => void;
  productSku: string; // Add the original product's SKU
}

interface AttributeValueCombination {
  attributeValueIds: number[];
  attributeValues: Record<number, AttributeValue>;
}

const VariantsStep = ({
  attributes,
  attributeValues,
  variants,
  onVariantsChange,
  productSku,
}: VariantsStepProps) => {
  const [error, setError] = useState<string | null>(null);
  const [combinations, setCombinations] = useState<AttributeValueCombination[]>([]);
  const [editingVariant, setEditingVariant] = useState<number | null>(null);
  const [unlimitedQuantityStates, setUnlimitedQuantityStates] = useState<Record<number, boolean>>({});

  // Generate all possible combinations of attribute values
  useEffect(() => {
    if (attributes.length === 0 || Object.keys(attributeValues).length === 0) {
      // Clear variants if there are no attributes
      if (variants.length > 0) {
        onVariantsChange([]);
        setCombinations([]);
      }
      return;
    }

    // Check if all attributes have values
    const allAttributesHaveValues = attributes.every(
      attr => attributeValues[attr.id!]?.length > 0
    );

    if (!allAttributesHaveValues) {
      setError("Some attributes don't have any values defined");
      return;
    }

    // Generate combinations
    const generateCombinations = (
      attributeIndex: number,
      currentCombination: AttributeValueCombination
    ): AttributeValueCombination[] => {
      if (attributeIndex >= attributes.length) {
        return [currentCombination];
      }

      const attribute = attributes[attributeIndex];
      const values = attributeValues[attribute.id!] || [];

      if (values.length === 0) {
        return generateCombinations(attributeIndex + 1, currentCombination);
      }

      return values.flatMap(value => {
        const newCombination: AttributeValueCombination = {
          attributeValueIds: [...currentCombination.attributeValueIds, value.id!],
          attributeValues: {
            ...currentCombination.attributeValues,
            [attribute.id!]: value,
          },
        };

        return generateCombinations(attributeIndex + 1, newCombination);
      });
    };

    const initialCombination: AttributeValueCombination = {
      attributeValueIds: [],
      attributeValues: {},
    };

    const generatedCombinations = generateCombinations(0, initialCombination);
    setCombinations(generatedCombinations);

    // Check if we need to regenerate variants (only if combinations changed)
    const existingCombinationKeys = variants.map(v => v.attributeValueIds.sort().join('-'));
    const newCombinationKeys = generatedCombinations.map(c => c.attributeValueIds.sort().join('-'));

    const combinationsChanged = existingCombinationKeys.length !== newCombinationKeys.length ||
      existingCombinationKeys.some(key => !newCombinationKeys.includes(key)) ||
      newCombinationKeys.some(key => !existingCombinationKeys.includes(key));

    // Only regenerate variants if the combinations actually changed
    if (combinationsChanged) {
      const initialVariants: ProductVariant[] = generatedCombinations.map(combination => {
          // Generate a SKU based on the attribute values
          const skuParts = attributes.map(attr => {
            const value = combination.attributeValues[attr.id!];
            // Format each attribute value as: value
            return value ? value.value.replace(/\s+/g, '-').toUpperCase() : '';
          });

          // Use the original product's SKU as a prefix for each variant's SKU
          // Sanitize the SKU to avoid any issues with special characters
          const sanitizedProductSku = productSku.replace(/\s+/g, '-').toUpperCase();
          // Format: {original-product-sku}-variant-value1-value2-...
          const sku = `${sanitizedProductSku}-variant-${skuParts.join('-')}`;

          // Use a default price since we no longer have price modifiers
          const basePrice = 0;

          return {
            sku,
            price: basePrice.toFixed(2),
            stockStatus: StockStatus.IN_STOCK,
            stockQuantity: null, // Start with unlimited quantity by default
            attributeValueIds: combination.attributeValueIds,
            images: [],
          };
        });

        console.log('VariantsStep: Generated variants with stockQuantity:', initialVariants.map(v => v.stockQuantity));
        onVariantsChange(initialVariants);

        // Reset unlimited quantity states when variants are regenerated
        setUnlimitedQuantityStates({});
    }

    setError(null);
  // Include variants in dependency but only check for structural changes
  }, [attributes, attributeValues, onVariantsChange, productSku, variants]);

  // Initialize unlimited quantity states based on variant stockQuantity values
  const stockQuantitySignature = variants.map(v => v.stockQuantity).join(',');
  useEffect(() => {
    const newUnlimitedStates: Record<number, boolean> = {};
    variants.forEach((variant, index) => {
      if (variant.stockQuantity === null) {
        newUnlimitedStates[index] = true;
        console.log(`VariantsStep: Initialized variant ${index} as unlimited (stockQuantity: null)`);
      }
    });
    console.log('VariantsStep: Setting unlimited states:', newUnlimitedStates);
    setUnlimitedQuantityStates(newUnlimitedStates);
  }, [stockQuantitySignature, variants]); // Only run when stockQuantity values change

  // Utility function to clean price values
  const cleanPriceValue = (value: string): string => {
    // Remove all non-numeric characters except decimal point
    return value.replace(/[^0-9.]/g, '');
  };

  // Handle variant field changes
  const handleVariantChange = (index: number, field: keyof ProductVariant, value: any) => {
    const updatedVariants = [...variants];

    // Special handling for SKU to ensure it maintains the required format
    if (field === 'sku') {
      // If the user is trying to edit the SKU, make sure it still has the product SKU prefix
      const sanitizedProductSku = productSku.replace(/\s+/g, '-').toUpperCase();
      const prefix = `${sanitizedProductSku}-variant-`;

      // If the user removed the prefix, add it back
      if (!value.startsWith(prefix)) {
        // Just use the user's input as the variant-specific part
        // This is simpler and more reliable than trying to extract parts
        value = `${prefix}${value}`;
      }
    } else if (field === 'price') {
      // Clean price values to remove commas, dollar signs, and other non-numeric characters
      value = cleanPriceValue(value);
    }

    updatedVariants[index] = {
      ...updatedVariants[index],
      [field]: value,
    };
    onVariantsChange(updatedVariants);
  };

  // Handle unlimited quantity toggle for variants
  const handleUnlimitedQuantityChange = (index: number, isUnlimited: boolean) => {
    console.log(`VariantsStep: Unlimited quantity change for variant ${index}:`, isUnlimited);
    setUnlimitedQuantityStates(prev => ({
      ...prev,
      [index]: isUnlimited
    }));

    const updatedVariants = [...variants];
    if (isUnlimited) {
      // Set stockQuantity to null for unlimited
      updatedVariants[index] = {
        ...updatedVariants[index],
        stockQuantity: null,
      };
      console.log(`VariantsStep: Set variant ${index} stockQuantity to null (unlimited)`);
    } else {
      // Set stockQuantity to 0 when unlimited is disabled
      updatedVariants[index] = {
        ...updatedVariants[index],
        stockQuantity: 0,
      };
      console.log(`VariantsStep: Set variant ${index} stockQuantity to 0 (limited)`);
    }
    onVariantsChange(updatedVariants);
  };

  // Handle image URL changes
  const [imageUrl, setImageUrl] = useState("");

  const handleAddImage = (variantIndex: number) => {
    if (!imageUrl.trim()) {
      setError("Please enter an image URL");
      return;
    }

    const updatedVariants = [...variants];
    const variant = updatedVariants[variantIndex];

    const newImage: ProductImage = {
      url: imageUrl,
      position: variant.images.length,
    };

    variant.images = [...variant.images, newImage];
    onVariantsChange(updatedVariants);
    setImageUrl("");
    setError(null);
  };

  const handleRemoveImage = (variantIndex: number, imageIndex: number) => {
    const updatedVariants = [...variants];
    const variant = updatedVariants[variantIndex];

    const updatedImages = [...variant.images];
    updatedImages.splice(imageIndex, 1);

    // Update positions
    const reorderedImages = updatedImages.map((img, idx) => ({
      ...img,
      position: idx,
    }));

    variant.images = reorderedImages;
    onVariantsChange(updatedVariants);
  };

  // Get attribute value display name
  const getAttributeValueName = (attributeId: number, valueId: number): string => {
    const values = attributeValues[attributeId] || [];
    const value = values.find(v => v.id === valueId);
    return value ? value.value : 'Unknown';
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium border-b pb-2">Configure Product Variants</h3>

      {error && (
        <div className="p-3 bg-red-100 text-red-700 rounded-md flex items-center">
          <FiAlertTriangle className="mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          Configure each product variant based on the attribute combinations.
        </p>
        <p className="text-sm text-gray-500 italic">
          Note: Each variant&apos;s SKU will be prefixed with the original product&apos;s SKU in the format:
          <span className="font-mono bg-gray-100 px-1">{productSku}-variant-[attribute-values]</span>
        </p>

        {variants.length === 0 ? (
          <div className="py-8 text-center text-gray-500">
            No variants generated yet. Make sure you have defined attributes and their values.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {attributes.map(attr => (
                    <th
                      key={attr.id}
                      className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {attr.name}
                    </th>
                  ))}
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    SKU
                  </th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock Status
                  </th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock Qty
                  </th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Images
                  </th>
                  <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {variants.map((variant, index) => (
                  <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    {/* Attribute values */}
                    {attributes.map(attr => {
                      const valueId = variant.attributeValueIds.find(id => {
                        const values = attributeValues[attr.id!] || [];
                        return values.some(v => v.id === id);
                      });

                      return (
                        <td key={attr.id} className="px-3 py-4 whitespace-nowrap text-sm">
                          {valueId ? getAttributeValueName(attr.id!, valueId) : 'N/A'}
                        </td>
                      );
                    })}

                    {/* SKU */}
                    <td className="px-3 py-4 whitespace-nowrap text-sm">
                      {editingVariant === index ? (
                        <input
                          type="text"
                          value={variant.sku}
                          onChange={e => handleVariantChange(index, 'sku', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md"
                        />
                      ) : (
                        variant.sku
                      )}
                    </td>

                    {/* Price */}
                    <td className="px-3 py-4 whitespace-nowrap text-sm">
                      {editingVariant === index ? (
                        <input
                          type="text"
                          value={variant.price}
                          onChange={e => handleVariantChange(index, 'price', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md"
                        />
                      ) : (
                        variant.price
                      )}
                    </td>

                    {/* Stock Status */}
                    <td className="px-3 py-4 whitespace-nowrap text-sm">
                      {editingVariant === index ? (
                        <select
                          value={variant.stockStatus}
                          onChange={e => handleVariantChange(index, 'stockStatus', e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded-md"
                        >
                          {Object.values(StockStatus).map(status => (
                            <option key={status} value={status}>
                              {status.replace('_', ' ')}
                            </option>
                          ))}
                        </select>
                      ) : (
                        variant.stockStatus.replace('_', ' ')
                      )}
                    </td>

                    {/* Stock Quantity */}
                    <td className="px-3 py-4 whitespace-nowrap text-sm">
                      {editingVariant === index ? (
                        <div className="space-y-2 min-w-[150px]">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id={`unlimited-${index}`}
                              checked={unlimitedQuantityStates[index] || variant.stockQuantity === null}
                              onChange={e => handleUnlimitedQuantityChange(index, e.target.checked)}
                              className="h-3 w-3 text-main-color focus:ring-main-color border-gray-300 rounded"
                            />
                            <label htmlFor={`unlimited-${index}`} className="ml-1 text-xs text-gray-700">
                              Unlimited
                            </label>
                          </div>
                          <input
                            type="number"
                            value={variant.stockQuantity || 0}
                            onChange={e => handleVariantChange(index, 'stockQuantity', parseInt(e.target.value) || 0)}
                            disabled={unlimitedQuantityStates[index] || variant.stockQuantity === null}
                            className={`w-full px-2 py-1 border border-gray-300 rounded-md text-xs ${
                              unlimitedQuantityStates[index] || variant.stockQuantity === null ? 'bg-gray-100 text-gray-500' : ''
                            }`}
                            placeholder={unlimitedQuantityStates[index] || variant.stockQuantity === null ? 'Unlimited' : '0'}
                          />
                        </div>
                      ) : (
                        variant.stockQuantity === null ? 'Unlimited' : (variant.stockQuantity || 'N/A')
                      )}
                    </td>

                    {/* Images */}
                    <td className="px-3 py-4 whitespace-nowrap text-sm">
                      {editingVariant === index ? (
                        <div className="space-y-2 min-w-[300px]">
                          <ImageManager
                            images={variant.images}
                            onImagesChange={(images) => {
                              const updatedVariants = [...variants];
                              updatedVariants[index].images = images;
                              onVariantsChange(updatedVariants);
                            }}
                            uploadType="variant-image"
                            maxImages={5}
                            allowReorder={true}
                            className="max-w-sm"
                          />

                          {/* Manual URL input as fallback */}
                          <div className="flex space-x-2">
                            <input
                              type="text"
                              value={imageUrl}
                              onChange={e => setImageUrl(e.target.value)}
                              placeholder="Or add URL manually"
                              className="flex-1 px-2 py-1 border border-gray-300 rounded-md text-xs"
                            />
                            <button
                              type="button"
                              onClick={() => handleAddImage(index)}
                              className="px-2 py-1 bg-main-color text-white rounded-md text-xs"
                            >
                              <FiPlus size={12} />
                            </button>
                          </div>
                        </div>
                      ) : (
                        <span>{variant.images.length} image(s)</span>
                      )}
                    </td>

                    {/* Actions */}
                    <td className="px-3 py-4 whitespace-nowrap text-sm">
                      {editingVariant === index ? (
                        <button
                          type="button"
                          onClick={() => setEditingVariant(null)}
                          className="text-green-500 hover:text-green-700"
                        >
                          <FiSave size={18} />
                        </button>
                      ) : (
                        <button
                          type="button"
                          onClick={() => setEditingVariant(index)}
                          className="text-blue-500 hover:text-blue-700"
                        >
                          <FiEdit size={18} />
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default VariantsStep;
