"use client";

import { useState, useEffect } from 'react';
import { SeoCheck } from 'seord';

interface SeoEvaluatorProps {
  title: string;
  content: string;
  type: 'title' | 'description' | 'shortDescription';
  keyword?: string;
}

const SeoEvaluator = ({ title, content, type, keyword = '' }: SeoEvaluatorProps) => {
  const [seoScore, setSeoScore] = useState<number | null>(null);
  const [seoMessages, setSeoMessages] = useState<{
    warnings: string[];
    goodPoints: string[];
    minorWarnings: string[];
  }>({ warnings: [], goodPoints: [], minorWarnings: [] });
  const [isEvaluating, setIsEvaluating] = useState(false);

  useEffect(() => {
    // Only evaluate if we have content and it's not too short
    if (content && content.length > 10) {
      console.log(`SEO Evaluator: Evaluating content for ${type}`, { title, contentLength: content.length });
      evaluateSeo();
    } else {
      console.log(`SEO Evaluator: Content too short for ${type}`, { title, contentLength: content?.length || 0 });
      setSeoScore(null);
      setSeoMessages({ warnings: [], goodPoints: [], minorWarnings: [] });
    }
  }, [content, title, keyword, type]);

  const evaluateSeo = () => {
    try {
      setIsEvaluating(true);

      // Extract plain text from HTML content
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = content;
      const plainText = tempDiv.textContent || tempDiv.innerText || '';

      // Different evaluation criteria based on content type
      let minLength = 10;
      let idealLength = 50;
      let maxScore = 95;
      let contentTypeLabel = "";

      // Set different criteria based on content type
      switch (type) {
        case 'title':
          minLength = 3;
          idealLength = 8;
          maxScore = 100;
          contentTypeLabel = "Product Title";
          break;
        case 'shortDescription':
          minLength = 10;
          idealLength = 30;
          maxScore = 95;
          contentTypeLabel = "Short Description";
          break;
        case 'description':
          minLength = 20;
          idealLength = 100;
          maxScore = 95;
          contentTypeLabel = "Full Description";
          break;
        default:
          contentTypeLabel = "Content";
      }

      // Skip evaluation if content is too short for its type
      if (plainText.length < minLength) {
        setIsEvaluating(false);
        return;
      }

      // Calculate score based on content type and length
      let mockScore;
      if (type === 'title') {
        // For titles, we want 5-10 words ideally
        const wordCount = plainText.split(/\s+/).filter(word => word.length > 0).length;
        mockScore = wordCount < 3 ? 50 :
                   wordCount <= 10 ? 90 + (wordCount / 10) * 10 :
                   Math.max(90 - ((wordCount - 10) * 5), 60);
      } else {
        // For descriptions, we want more content
        const ratio = plainText.length / idealLength;
        mockScore = ratio < 0.5 ? 40 + (ratio * 60) :
                   ratio <= 2 ? 70 + (ratio * 15) :
                   Math.max(100 - ((ratio - 2) * 10), 60);
      }

      mockScore = Math.min(Math.max(Math.round(mockScore), 30), maxScore);

      console.log(`SEO Evaluator: Analysis for ${contentTypeLabel}`, {
        contentLength: plainText.length,
        mockScore: mockScore
      });

      // Extract keywords for display
      const keywords = extractKeywords(plainText, title);

      // Generate appropriate feedback based on content type
      let warnings = [];
      let goodPoints = [];

      if (type === 'title') {
        if (plainText.length < 5) {
          warnings.push('Title is too short. Add more descriptive words.');
        } else if (plainText.split(/\s+/).length > 10) {
          warnings.push('Title is too long. Keep it concise (5-10 words).');
        } else {
          goodPoints.push('Title has a good length.');
        }

        if (keywords.length < 2) {
          warnings.push('Include more relevant keywords in the title.');
        } else {
          goodPoints.push(`Found ${keywords.length} potential keywords.`);
        }
      } else if (type === 'shortDescription') {
        if (plainText.length < 30) {
          warnings.push('Short description could be more detailed (30-150 characters ideal).');
        } else if (plainText.length > 300) {
          warnings.push('Short description is quite long. Consider making it more concise.');
        } else {
          goodPoints.push('Short description has a good length.');
        }

        if (keywords.length < 2) {
          warnings.push('Include more relevant keywords in the short description.');
        } else {
          goodPoints.push(`Found ${keywords.length} potential keywords.`);
        }
      } else if (type === 'description') {
        if (plainText.length < 100) {
          warnings.push('Full description is too short. Add more detailed information.');
        } else if (plainText.length > 1000) {
          warnings.push('Description is very long. Consider breaking it into paragraphs.');
        } else {
          goodPoints.push('Description has a good length.');
        }

        if (keywords.length < 3) {
          warnings.push('Include more relevant keywords in the description.');
        } else {
          goodPoints.push(`Found ${keywords.length} potential keywords.`);
        }
      }

      // Update state with results
      setSeoScore(mockScore);
      setSeoMessages({
        warnings: warnings,
        goodPoints: goodPoints,
        minorWarnings: []
      });

      // Update the score circle if it exists
      const scoreId = `${type}SeoScore`;
      const scoreElement = document.getElementById(scoreId);
      if (scoreElement) {
        scoreElement.textContent = Math.round(mockScore).toString();

        // Update the color based on the score
        const circleElement = scoreElement.parentElement;
        if (circleElement) {
          circleElement.className = "w-12 h-12 rounded-full flex items-center justify-center relative";

          if (mockScore >= 80) {
            circleElement.classList.add("bg-green-100", "border-green-300", "text-green-800");
          } else if (mockScore >= 60) {
            circleElement.classList.add("bg-yellow-100", "border-yellow-300", "text-yellow-800");
          } else {
            circleElement.classList.add("bg-red-100", "border-red-300", "text-red-800");
          }
        }
      }

      /* Commented out the SEOrd implementation for now
      // Determine the main keyword if not provided
      const mainKeyword = keyword || extractMainKeyword(title);

      // Create subkeywords from title words
      const subKeywords = extractSubKeywords(title, mainKeyword);

      // Prepare content for SEO analysis
      const contentJson = {
        title: title,
        htmlText: `<h1>${title}</h1><p>${content}</p>`,
        keyword: mainKeyword,
        subKeywords: subKeywords,
        metaDescription: type === 'description' ? plainText : '',
        languageCode: 'en',
        countryCode: 'us'
      };

      // Initialize SEO check
      const seoCheck = new SeoCheck(contentJson, 'example.com');

      // Analyze SEO
      const result = seoCheck.analyzeSeo();

      console.log(`SEO Evaluator: Analysis complete for ${type}`, {
        score: result.seoScore,
        warnings: result.messages.warnings.length,
        goodPoints: result.messages.goodPoints.length
      });

      // Update state with results
      setSeoScore(result.seoScore);
      setSeoMessages({
        warnings: result.messages.warnings,
        goodPoints: result.messages.goodPoints,
        minorWarnings: result.messages.minorWarnings
      });
      */
    } catch (error) {
      console.error('SEO evaluation error:', error);
      // Set a default score to ensure something is displayed
      setSeoScore(50);
      setSeoMessages({
        warnings: ['Error analyzing content. Try adding more detailed text.'],
        goodPoints: [],
        minorWarnings: []
      });
    } finally {
      setIsEvaluating(false);
    }
  };

  // Extract keywords from text
  const extractKeywords = (text: string, titleText: string = ''): string[] => {
    if (!text) return [];

    // Expanded list of stop words
    const stopWords = [
      'a', 'an', 'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by',
      'of', 'as', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
      'do', 'does', 'did', 'will', 'would', 'shall', 'should', 'can', 'could', 'may', 'might',
      'must', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
      'their', 'our', 'your', 'my', 'his', 'her', 'its', 'from', 'which', 'who', 'whom', 'whose'
    ];

    // Clean and normalize text
    const cleanText = text.toLowerCase()
      .replace(/<[^>]*>/g, ' ')  // Remove HTML tags
      .replace(/[^\w\s]/g, ' ')  // Remove punctuation
      .replace(/\s+/g, ' ')      // Replace multiple spaces with single space
      .trim();

    // Extract words and filter out stop words and short words
    const words = cleanText.split(/\s+/)
      .filter(word => !stopWords.includes(word) && word.length > 2);

    // Count word frequency
    const wordFrequency: {[key: string]: number} = {};
    words.forEach(word => {
      wordFrequency[word] = (wordFrequency[word] || 0) + 1;
    });

    // Also consider words from the title if provided
    if (titleText) {
      const titleWords = titleText.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => !stopWords.includes(word) && word.length > 2);

      titleWords.forEach(word => {
        // Title words get a higher weight
        wordFrequency[word] = (wordFrequency[word] || 0) + 2;
      });
    }

    // Convert to array of [word, frequency] pairs and sort by frequency
    const sortedWords = Object.entries(wordFrequency)
      .sort((a, b) => b[1] - a[1])
      .map(entry => entry[0]);

    // Return top keywords (up to 10)
    return sortedWords.slice(0, 10);
  };

  // Extract main keyword (for backward compatibility)
  const extractMainKeyword = (text: string): string => {
    const keywords = extractKeywords(text);
    return keywords.length > 0 ? keywords[0] : '';
  };

  // Extract sub-keywords (for backward compatibility)
  const extractSubKeywords = (text: string, mainKeyword: string): string[] => {
    const allKeywords = extractKeywords(text);
    return allKeywords.filter(word => word !== mainKeyword).slice(0, 5);
  };

  if (!content || content.length < 10) {
    return null;
  }

  // Extract keywords for display
  const detectedKeywords = extractKeywords(content, title);

  // Get content type label
  const getContentTypeLabel = () => {
    switch (type) {
      case 'title': return 'Product Title';
      case 'shortDescription': return 'Short Description';
      case 'description': return 'Full Description';
      default: return 'Content';
    }
  };

  return (
    <div className="mt-6 mb-6 text-sm p-4 border border-gray-200 rounded-md bg-gray-50 shadow-sm z-10 relative">
      <h4 className="font-medium text-gray-700 mb-3 text-base">SEO Analysis - {getContentTypeLabel()}</h4>
      {isEvaluating ? (
        <p className="text-gray-500">Evaluating SEO...</p>
      ) : seoScore !== null ? (
        <div>
          <div className="flex items-center mb-3">
            <span className="font-medium mr-2">SEO Score:</span>
            <div className="flex items-center">
              <div className="w-40 h-4 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full ${
                    seoScore >= 80 ? 'bg-green-500' :
                    seoScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${seoScore}%` }}
                ></div>
              </div>
              <span className="ml-2 font-medium">{Math.round(seoScore)}</span>
            </div>
          </div>

          {/* Keywords section */}
          <div className="mb-3">
            <details open>
              <summary className="text-blue-600 cursor-pointer hover:underline font-medium">
                Detected Keywords ({detectedKeywords.length})
              </summary>
              {detectedKeywords.length > 0 ? (
                <div className="mt-2 flex flex-wrap gap-1">
                  {detectedKeywords.map((keyword, index) => (
                    <span
                      key={index}
                      className={`px-2 py-1 rounded-full text-xs ${
                        index === 0 ? 'bg-blue-100 text-blue-800 font-medium' : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {keyword}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-xs text-gray-500 mt-1">No keywords detected. Add more descriptive content.</p>
              )}
            </details>
          </div>

          {seoMessages.warnings.length > 0 && (
            <div className="mb-3">
              <details>
                <summary className="text-red-600 cursor-pointer hover:underline">
                  {seoMessages.warnings.length} {seoMessages.warnings.length === 1 ? 'warning' : 'warnings'}
                </summary>
                <ul className="pl-5 mt-1 list-disc text-red-600">
                  {seoMessages.warnings.map((warning, index) => (
                    <li key={index} className="text-xs">{warning}</li>
                  ))}
                </ul>
              </details>
            </div>
          )}

          {seoMessages.goodPoints.length > 0 && (
            <div className="mb-3">
              <details>
                <summary className="text-green-600 cursor-pointer hover:underline">
                  {seoMessages.goodPoints.length} good {seoMessages.goodPoints.length === 1 ? 'point' : 'points'}
                </summary>
                <ul className="pl-5 mt-1 list-disc text-green-600">
                  {seoMessages.goodPoints.map((point, index) => (
                    <li key={index} className="text-xs">{point}</li>
                  ))}
                </ul>
              </details>
            </div>
          )}

          {/* Tips based on content type */}
          <div className="mt-3 text-xs text-gray-600">
            <details>
              <summary className="cursor-pointer hover:underline">SEO Tips for {getContentTypeLabel()}</summary>
              <ul className="pl-5 mt-1 list-disc">
                {type === 'title' && (
                  <>
                    <li>Keep titles between 5-10 words for optimal SEO</li>
                    <li>Include your main keyword near the beginning</li>
                    <li>Make it descriptive but concise</li>
                  </>
                )}
                {type === 'shortDescription' && (
                  <>
                    <li>Aim for 30-150 characters</li>
                    <li>Include 2-3 important keywords</li>
                    <li>Make it compelling to encourage clicks</li>
                  </>
                )}
                {type === 'description' && (
                  <>
                    <li>Include at least 100-300 words for good SEO</li>
                    <li>Use headings, lists, and formatting to improve readability</li>
                    <li>Include your main keywords naturally throughout the text</li>
                    <li>Add specific product details, benefits, and use cases</li>
                  </>
                )}
              </ul>
            </details>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default SeoEvaluator;
