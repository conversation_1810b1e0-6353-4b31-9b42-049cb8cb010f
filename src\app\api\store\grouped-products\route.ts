import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Log the request body in a detailed format
    console.log(`API proxy - Creating grouped product with body:`, JSON.stringify(body, null, 2));

    const backendUrl = `${API_BASE_URL}${API_PATH_PREFIX}/grouped-products`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    // Log the response status
    console.log(`API proxy - Response status for creating grouped product: ${response.status}`);

    // If the response is not ok, log the error response
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy - Error response from backend:`, errorText);
      return NextResponse.json(
        { error: 'Failed to create grouped product', details: errorText },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /grouped-products):', error);
    return NextResponse.json(
      { error: 'Failed to create grouped product', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
