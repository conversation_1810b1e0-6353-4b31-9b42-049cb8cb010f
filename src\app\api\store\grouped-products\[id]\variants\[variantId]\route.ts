import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_STORE_PATH_PREFIX = process.env.NEXT_PUBLIC_API_STORE_PATH_PREFIX || '/api/store';

// PUT handler for updating grouped product variant
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; variantId: string } }
) {
  try {
    const { id: productId, variantId } = params;
    const body = await request.json();

    console.log(`API proxy - Updating grouped product variant with product ID ${productId}, variant ID ${variantId}:`, body);

    const backendUrl = `${API_BASE_URL}${API_STORE_PATH_PREFIX}/grouped-products/${productId}/variants/${variantId}`;
    console.log(`API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API proxy error - Failed to update grouped product variant (${response.status}):`, errorText);
      return NextResponse.json(
        { error: `Failed to update grouped product variant: ${errorText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`API proxy - Successfully updated grouped product variant:`, data);
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (PUT /grouped-products/[id]/variants/[variantId]):', error);
    return NextResponse.json(
      { error: 'Failed to update grouped product variant' },
      { status: 500 }
    );
  }
}
