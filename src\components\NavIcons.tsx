"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FiShoppingBag } from "react-icons/fi";
import CartModal from "./CartModal";
import { useAuthStore } from "@/hooks/useAuthStore";
import { useCartStore } from "@/hooks/useCartStore";

const NavIcons = () => {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  const router = useRouter();
  const pathName = usePathname();

  const { user, isAuthenticated, logout, isLoading } = useAuthStore();
  const { counter = 1 } = useCartStore();

  // Check if user is authenticated on component mount
  useEffect(() => {
    if (isAuthenticated() && !user) {
      // If authenticated but no user data, fetch profile
      useAuthStore.getState().getProfile();
    }
  }, [isAuthenticated, user]);

  const handleProfile = () => {
    if (!isAuthenticated()) {
      router.push("/login");
    } else {
      setIsProfileOpen((prev) => !prev);
    }
  };

  const navigateToProfile = () => {
    setIsProfileOpen(false);
    router.push("/profile");
  };

  const handleLogout = async () => {
    await logout();
    setIsProfileOpen(false);
    router.push("/login");
  };

  return (
    <div className="flex items-center gap-4 xl:gap-6 relative">
      <Image
        src="/profile.png"
        alt=""
        width={22}
        height={22}
        className="cursor-pointer"
        onClick={handleProfile}
      />
      {isProfileOpen && isAuthenticated() && (
        <div className="absolute p-4 rounded-md top-12 left-0 bg-white text-sm shadow-[0_3px_10px_rgb(0,0,0,0.2)] z-[150]">
          <div className="cursor-pointer hover:text-main-color" onClick={navigateToProfile}>Profile</div>
          <div className="mt-2 cursor-pointer hover:text-main-color" onClick={handleLogout}>
            {isLoading ? "Logging out..." : "Logout"}
          </div>
        </div>
      )}
      <Image
        src="/notification.png"
        alt=""
        width={22}
        height={22}
        className="cursor-pointer"
      />
      <div
        className="relative cursor-pointer"
        onClick={() => setIsCartOpen((prev) => !prev)}
      >
        <FiShoppingBag size={22} className="text-gray-700 hover:text-main-color transition-colors" />
        <div className="absolute -top-4 -right-4 w-6 h-6 bg-main-color rounded-full text-white text-sm flex items-center justify-center">
          {counter}
        </div>
      </div>
      {isCartOpen && <CartModal />}
    </div>
  );
};

export default NavIcons;