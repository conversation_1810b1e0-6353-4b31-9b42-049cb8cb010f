import { NextRequest, NextResponse } from 'next/server';

// GET handler for directly testing product fetching
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = params.id;
    console.log(`Direct Test API - Fetching product with ID ${id}`);

    // Get the API base URL and path prefix from environment variables
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

    // Determine if the ID is numeric or a slug
    const isNumeric = /^\d+$/.test(id);

    // Direct call to the backend API using environment variables
    let backendUrl;
    if (isNumeric) {
      // If it's numeric, use the ID endpoint
      backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products/${id}`;
    } else {
      // If it's a slug, use the slug endpoint
      backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products/slug/${id}`;
    }

    console.log(`Direct Test API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store'
    });

    if (!response.ok) {
      const errorStatus = response.status;
      let errorMessage = `Product with ID ${id} not found`;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // If we can't parse the error response, use the default message
      }

      return NextResponse.json(
        { message: errorMessage, error: response.statusText, statusCode: errorStatus },
        { status: errorStatus }
      );
    }

    const data = await response.json();
    console.log('Direct Test API - Successfully fetched product data:', data);
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Direct Test API error (GET /direct-test/${params.id}):`, error);
    return NextResponse.json(
      { message: `Failed to fetch product with ID ${params.id}`, error: 'Internal Server Error', statusCode: 500 },
      { status: 500 }
    );
  }
}
