"use client";

import { useEffect, useState } from "react";
import { useProductSectionStore } from "@/hooks/useSectionStore";
import { useProductStore } from "@/hooks/useProductStore";
import { FiArrowLeft, FiTrash2, <PERSON>AlertCircle, FiRefreshCw, FiPlus, FiSearch } from "react-icons/fi";
import { ProductSection, ProductSectionItem } from "@/services/api";
import LoadingSpinner from "../common/LoadingSpinner";
import AddProductToSectionModal from "./AddProductToSectionModal";

interface ProductSectionItemsViewProps {
  sectionId: number;
  onBack: () => void;
}

const ProductSectionItemsView = ({ sectionId, onBack }: ProductSectionItemsViewProps) => {
  const { fetchSectionById, removeProductFromSection, isLoading, error, clearError } = useProductSectionStore();
  const [section, setSection] = useState<ProductSection | null>(null);
  const [isAddProductModalOpen, setIsAddProductModalOpen] = useState(false);
  const [isRemoving, setIsRemoving] = useState<number | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    const loadData = async () => {
      clearError();
      setFetchError(null);
      try {
        const sectionData = await fetchSectionById(sectionId);
        if (sectionData) {
          setSection(sectionData);
        } else {
          setFetchError("Failed to fetch section data");
        }
      } catch (error) {
        setFetchError(error instanceof Error ? error.message : 'Failed to fetch section data');
      }
    };

    loadData();
  }, [sectionId, fetchSectionById, clearError]);

  const handleRemoveProduct = async (itemId: number) => {
    setIsRemoving(itemId);
    try {
      const updatedSection = await removeProductFromSection(sectionId, itemId);
      if (updatedSection) {
        setSection(updatedSection);
      }
    } catch (error) {
      console.error(`Failed to remove item ${itemId} from section ${sectionId}:`, error);
    } finally {
      setIsRemoving(null);
    }
  };

  const handleRefresh = async () => {
    clearError();
    setFetchError(null);
    try {
      const sectionData = await fetchSectionById(sectionId);
      if (sectionData) {
        setSection(sectionData);
      } else {
        setFetchError("Failed to fetch section data");
      }
    } catch (error) {
      setFetchError(error instanceof Error ? error.message : 'Failed to fetch section data');
    }
  };

  const handleAddProductSuccess = (updatedSection: ProductSection) => {
    setSection(updatedSection);
    setIsAddProductModalOpen(false);
  };

  const items = section?.items || [];
  const filteredItems = searchTerm
    ? items.filter(
        (item) =>
          item.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.product.sku.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : items;

  if (isLoading && !section) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if ((error || fetchError) && !section) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <FiAlertCircle className="text-red-500 w-12 h-12 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load section data</h3>
        <p className="text-sm text-gray-500 mb-4">{error || fetchError}</p>
        <button
          onClick={handleRefresh}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
        >
          <FiRefreshCw className="mr-2 -ml-1 h-4 w-4" />
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <button
          onClick={onBack}
          className="mr-4 text-gray-500 hover:text-gray-700"
          title="Back to Sections"
        >
          <FiArrowLeft size={20} />
        </button>
        <h2 className="text-xl font-semibold">
          {section?.name || `Section #${sectionId}`} - Products
        </h2>
      </div>

      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-64">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <FiSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search by name or SKU"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-3 py-2 w-full border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-main-color focus:border-main-color"
          />
        </div>
        <button
          onClick={() => setIsAddProductModalOpen(true)}
          className="bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition flex items-center"
        >
          <FiPlus className="mr-2" />
          Add Product
        </button>
      </div>

      {isLoading && (
        <div className="flex justify-center mb-4">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-sm text-gray-500">Refreshing...</span>
        </div>
      )}

      {filteredItems.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border border-dashed border-gray-300">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products in this section</h3>
          <p className="text-sm text-gray-500 mb-4">
            {searchTerm ? "No products match your search criteria." : "Add products to this section to get started."}
          </p>
          {!searchTerm && (
            <button
              onClick={() => setIsAddProductModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
            >
              <FiPlus className="mr-2 -ml-1 h-4 w-4" />
              Add Product
            </button>
          )}
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Position
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Image
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stock
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredItems.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.position}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100 border border-gray-200">
                      {item.product.imageUrl ? (
                        <img
                          src={item.product.imageUrl}
                          alt={item.product.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center text-gray-400 text-xs">
                          No image
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.product.sku}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.product.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${item.product.price}
                    {item.product.salePrice && (
                      <span className="ml-2 line-through text-gray-400">${item.product.salePrice}</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.product.stockStatus}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleRemoveProduct(item.id)}
                      className="text-red-600 hover:text-red-900"
                      disabled={isRemoving === item.id}
                      title="Remove from Section"
                    >
                      {isRemoving === item.id ? (
                        <LoadingSpinner size="sm" />
                      ) : (
                        <FiTrash2 size={18} />
                      )}
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {section && (
        <AddProductToSectionModal
          isOpen={isAddProductModalOpen}
          sectionId={sectionId}
          onClose={() => setIsAddProductModalOpen(false)}
          onSuccess={handleAddProductSuccess}
          existingProductIds={items.map(item => item.product.id)}
        />
      )}
    </div>
  );
};

export default ProductSectionItemsView;
