import { create } from "zustand";
import {
  Product,
  productApi,
  GroupedProductData,
  GroupedProductUpdateData,
  groupedProductApi,
  ProductVariant
} from "@/services/api";

interface PaginatedResponse<T> {
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  data: T[];
}

interface ProductState {
  products: Product[];
  productsByCategory: Record<number, Product[]>;
  productsPagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  categoryProductsPagination: Record<number, {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }>;
  isLoading: boolean;
  error: string | null;
  selectedProduct: Product | null;

  // Actions
  fetchProducts: (page?: number, limit?: number, search?: string) => Promise<PaginatedResponse<Product> | null>;
  fetchProductById: (id: number) => Promise<Product | null>;
  fetchProductsByCategory: (categoryId: number, page?: number, limit?: number, search?: string) => Promise<PaginatedResponse<Product> | null>;
  createProduct: (product: Partial<Product>) => Promise<Product | null>;
  createProductForCategory: (categoryId: number, product: Partial<Product>) => Promise<Product | null>;
  updateProduct: (id: number, product: Partial<Product>) => Promise<Product | null>;
  deleteProduct: (id: number) => Promise<Product | null>;
  setSelectedProduct: (product: Product | null) => void;
  clearError: () => void;

  // Grouped product actions
  fetchGroupedProductById: (id: number) => Promise<Product | null>;
  createGroupedProduct: (product: GroupedProductData) => Promise<Product | null>;
  updateGroupedProduct: (id: number, product: GroupedProductUpdateData) => Promise<Product | null>;
  updateGroupedProductBase: (id: number, baseData: Partial<GroupedProductUpdateData>) => Promise<Product | null>;
  updateGroupedProductVariant: (productId: number, variantId: number, variantData: Partial<ProductVariant>) => Promise<Product | null>;
  deleteGroupedProduct: (id: number) => Promise<Product | null>;
}

export const useProductStore = create<ProductState>((set, get) => ({
  products: [],
  productsByCategory: {},
  productsPagination: null,
  categoryProductsPagination: {},
  isLoading: false,
  error: null,
  selectedProduct: null,

  clearError: () => set({ error: null }),

  fetchProducts: async (page: number = 1, limit: number = 20, search?: string) => {
    set({ isLoading: true, error: null });
    try {
      const response = await productApi.getAllPaginated(page, limit, search);

      console.log(`Product Store: Received ${response?.data?.length || 0} products, page ${response?.pagination?.page || 'unknown'}`);

      // Ensure response and response.data are valid
      if (!response || !response.data || !Array.isArray(response.data)) {
        console.error('Invalid products response format:', { hasResponse: !!response, hasData: !!(response?.data), dataIsArray: Array.isArray(response?.data) });
        throw new Error('Invalid response format from API');
      }
      set({
        products: response.data,
        productsPagination: response.pagination,
        isLoading: false
      });
      return response;
    } catch (error) {
      console.error('Store error - fetchProducts:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoading: false,
        products: [], // Reset products on error
        productsPagination: null
      });
      return null;
    }
  },

  fetchProductById: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Store - Fetching product with ID ${id}...`);
      const product = await productApi.getById(id);
      console.log(`Store - Fetched product with ID ${id}:`, product);

      // Update the product in the products list if it exists
      set(state => ({
        products: state.products.map(p => p.id === id ? product : p),
        isLoading: false
      }));

      return product;
    } catch (error) {
      console.error(`Store error - fetchProductById (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to fetch product with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  fetchProductsByCategory: async (categoryId: number, page: number = 1, limit: number = 20, search?: string) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Fetching products for category ID ${categoryId} from store - page: ${page}, limit: ${limit}, search: ${search || 'none'}`);
      const response = await productApi.getByCategoryPaginated(categoryId, page, limit, search);

      console.log(`Category ${categoryId} Store: Received ${response?.data?.length || 0} products, page ${response?.pagination?.page || 'unknown'}`);

      // Ensure response and response.data are valid
      if (!response || !response.data || !Array.isArray(response.data)) {
        console.error(`Invalid category ${categoryId} products response:`, { hasResponse: !!response, hasData: !!(response?.data), dataIsArray: Array.isArray(response?.data) });
        throw new Error('Invalid response format from API');
      }

      set(state => {
        console.log(`Updating state with paginated products for category ID ${categoryId}`);
        return {
          productsByCategory: {
            ...state.productsByCategory,
            [categoryId]: response.data
          },
          categoryProductsPagination: {
            ...state.categoryProductsPagination,
            [categoryId]: response.pagination
          },
          isLoading: false
        };
      });

      return response;
    } catch (error) {
      console.error(`Store error - fetchProductsByCategory (categoryId: ${categoryId}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to fetch products for category ID ${categoryId}`,
        isLoading: false
      });
      return null;
    }
  },

  createProduct: async (product: Partial<Product>) => {
    set({ isLoading: true, error: null });
    try {
      console.log('Store - Creating product:', product);

      const newProduct = await productApi.create(product);
      console.log('Store - Created product:', newProduct);

      if (!newProduct) {
        throw new Error('Failed to create product. No response from server.');
      }

      // Update the products list
      set(state => ({
        products: [...state.products, newProduct],
        isLoading: false
      }));

      // Update products by category if this product belongs to any categories
      if (product.categoryIds && product.categoryIds.length > 0) {
        product.categoryIds.forEach(categoryId => {
          const categoryProducts = get().productsByCategory[categoryId] || [];
          set(state => ({
            productsByCategory: {
              ...state.productsByCategory,
              [categoryId]: [...categoryProducts, newProduct]
            }
          }));
        });
      }

      return newProduct;
    } catch (error) {
      console.error('Store error - createProduct:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create product',
        isLoading: false
      });
      return null;
    }
  },

  createProductForCategory: async (categoryId: number, product: Partial<Product>) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Store - Creating product for category ID ${categoryId}:`, product);

      const newProduct = await productApi.createForCategory(categoryId, product);
      console.log(`Store - Created product for category ID ${categoryId}:`, newProduct);

      if (!newProduct) {
        throw new Error(`Failed to create product for category ID ${categoryId}. No response from server.`);
      }

      // Update both the main products list and the category-specific list
      set(state => ({
        products: [...state.products, newProduct],
        productsByCategory: {
          ...state.productsByCategory,
          [categoryId]: [...(state.productsByCategory[categoryId] || []), newProduct]
        },
        isLoading: false
      }));

      return newProduct;
    } catch (error) {
      console.error(`Store error - createProductForCategory (categoryId: ${categoryId}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to create product for category ID ${categoryId}`,
        isLoading: false
      });
      return null;
    }
  },

  updateProduct: async (id: number, product: Partial<Product>) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Updating product with ID ${id}:`, product);

      // Get the existing product to ensure we have all fields
      const existingProduct = get().products.find(p => p.id === id);
      if (!existingProduct) {
        console.warn(`Product with ID ${id} not found in store, proceeding with update anyway`);
      }

      // Create a clean merged product
      let mergedProduct: Partial<Product>;

      if (existingProduct) {
        // Start with existing product data
        mergedProduct = { ...existingProduct };

        // Apply updates from the product parameter
        Object.keys(product).forEach(key => {
          const typedKey = key as keyof Product;

          // Special handling for stockQuantity
          if (typedKey === 'stockQuantity') {
            const stockQuantity = product[typedKey] as number;
            if (stockQuantity !== 0) {
              mergedProduct[typedKey] = stockQuantity;
            } else {
              // If stockQuantity is 0, remove it from the merged product
              delete mergedProduct[typedKey];
            }
          } else {
            // For all other fields, just copy the value
            mergedProduct[typedKey] = product[typedKey] as any;
          }
        });
      } else {
        // If no existing product, just use the product parameter
        mergedProduct = { ...product };

        // Special handling for stockQuantity if it's 0
        if (mergedProduct.stockQuantity === 0) {
          delete mergedProduct.stockQuantity;
        }
      }

      // For simple products, ensure we don't send any ProductAttribute field
      // This will be handled by separate routes for grouped products

      console.log(`Merged product data for update:`, mergedProduct);

      const updatedProduct = await productApi.update(id, mergedProduct);
      console.log(`Received updated product from API:`, updatedProduct);

      // Update the product in the products list
      set(state => ({
        products: state.products.map(p =>
          p.id === id ? updatedProduct : p
        ),
        isLoading: false
      }));

      // Update the product in any category lists it belongs to
      if (updatedProduct.categories) {
        updatedProduct.categories.forEach(category => {
          if (category.id) {
            const categoryProducts = get().productsByCategory[category.id] || [];
            if (categoryProducts.length > 0) {
              set(state => ({
                productsByCategory: {
                  ...state.productsByCategory,
                  [category.id!]: categoryProducts.map(p =>
                    p.id === id ? updatedProduct : p
                  )
                }
              }));
            }
          }
        });
      }

      return updatedProduct;
    } catch (error) {
      console.error(`Store error - updateProduct (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update product with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  deleteProduct: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const deletedProduct = await productApi.delete(id);

      // Remove the product from the products list
      set(state => ({
        products: state.products.filter(p => p.id !== id),
        isLoading: false
      }));

      // Remove the product from any category lists it belongs to
      Object.keys(get().productsByCategory).forEach(categoryIdStr => {
        const categoryId = parseInt(categoryIdStr);
        const categoryProducts = get().productsByCategory[categoryId] || [];
        if (categoryProducts.some(p => p.id === id)) {
          set(state => ({
            productsByCategory: {
              ...state.productsByCategory,
              [categoryId]: categoryProducts.filter(p => p.id !== id)
            }
          }));
        }
      });

      return deletedProduct;
    } catch (error) {
      console.error(`Store error - deleteProduct (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete product with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  setSelectedProduct: (product: Product | null) => {
    set({ selectedProduct: product });
  },

  // Grouped product methods
  fetchGroupedProductById: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Store - Fetching grouped product with ID ${id}...`);
      const product = await groupedProductApi.getById(id);
      console.log(`Store - Fetched grouped product with ID ${id}:`, product);

      // Update the product in the products list if it exists
      set(state => ({
        products: state.products.map(p => p.id === id ? product : p),
        isLoading: false
      }));

      return product;
    } catch (error) {
      console.error(`Store error - fetchGroupedProductById (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to fetch grouped product with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  createGroupedProduct: async (product: GroupedProductData) => {
    set({ isLoading: true, error: null });
    try {
      console.log('Store - Creating grouped product:', JSON.stringify(product, null, 2));

      const newProduct = await groupedProductApi.createGroupedProduct(product);
      console.log('Store - Created grouped product:', newProduct);

      if (!newProduct) {
        throw new Error('Failed to create grouped product. No response from server.');
      }

      // Update the products list
      set(state => ({
        products: [...state.products, newProduct],
        isLoading: false
      }));

      // Update products by category if this product belongs to any categories
      if (product.categoryIds && product.categoryIds.length > 0) {
        product.categoryIds.forEach(categoryId => {
          const categoryProducts = get().productsByCategory[categoryId] || [];
          set(state => ({
            productsByCategory: {
              ...state.productsByCategory,
              [categoryId]: [...categoryProducts, newProduct]
            }
          }));
        });
      }

      return newProduct;
    } catch (error) {
      console.error('Store error - createGroupedProduct:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create grouped product',
        isLoading: false
      });
      return null;
    }
  },

  updateGroupedProduct: async (id: number, product: GroupedProductUpdateData) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Updating grouped product with ID ${id}:`, product);

      const updatedProduct = await groupedProductApi.updateGroupedProduct(id, product);
      console.log(`Received updated grouped product from API:`, updatedProduct);

      // Update the product in the products list
      set(state => ({
        products: state.products.map(p =>
          p.id === id ? updatedProduct : p
        ),
        isLoading: false
      }));

      // Update the product in any category lists it belongs to
      if (updatedProduct.categories) {
        updatedProduct.categories.forEach(category => {
          if (category.id) {
            const categoryProducts = get().productsByCategory[category.id] || [];
            if (categoryProducts.length > 0) {
              set(state => ({
                productsByCategory: {
                  ...state.productsByCategory,
                  [category.id!]: categoryProducts.map(p =>
                    p.id === id ? updatedProduct : p
                  )
                }
              }));
            }
          }
        });
      }

      return updatedProduct;
    } catch (error) {
      console.error(`Store error - updateGroupedProduct (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update grouped product with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  updateGroupedProductBase: async (id: number, baseData: Partial<GroupedProductUpdateData>) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Updating grouped product base with ID ${id}:`, baseData);

      const updatedProduct = await groupedProductApi.updateGroupedProductBase(id, baseData);
      console.log(`Received updated grouped product from API:`, updatedProduct);

      // Update the product in the products list
      set(state => ({
        products: state.products.map(p =>
          p.id === id ? updatedProduct : p
        ),
        isLoading: false
      }));

      // Update the product in any category lists it belongs to
      if (updatedProduct.categories) {
        updatedProduct.categories.forEach(category => {
          if (category.id) {
            const categoryProducts = get().productsByCategory[category.id] || [];
            if (categoryProducts.length > 0) {
              set(state => ({
                productsByCategory: {
                  ...state.productsByCategory,
                  [category.id!]: categoryProducts.map(p =>
                    p.id === id ? updatedProduct : p
                  )
                }
              }));
            }
          }
        });
      }

      return updatedProduct;
    } catch (error) {
      console.error(`Store error - updateGroupedProductBase (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update grouped product base with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  updateGroupedProductVariant: async (productId: number, variantId: number, variantData: Partial<ProductVariant>) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Updating grouped product variant with product ID ${productId}, variant ID ${variantId}:`, variantData);

      const updatedProduct = await groupedProductApi.updateGroupedProductVariant(productId, variantId, variantData);
      console.log(`Received updated grouped product from API:`, updatedProduct);

      // Update the product in the products list
      set(state => ({
        products: state.products.map(p =>
          p.id === productId ? updatedProduct : p
        ),
        isLoading: false
      }));

      // Update the product in any category lists it belongs to
      if (updatedProduct.categories) {
        updatedProduct.categories.forEach(category => {
          if (category.id) {
            const categoryProducts = get().productsByCategory[category.id] || [];
            if (categoryProducts.length > 0) {
              set(state => ({
                productsByCategory: {
                  ...state.productsByCategory,
                  [category.id!]: categoryProducts.map(p =>
                    p.id === productId ? updatedProduct : p
                  )
                }
              }));
            }
          }
        });
      }

      return updatedProduct;
    } catch (error) {
      console.error(`Store error - updateGroupedProductVariant (Product ID: ${productId}, Variant ID: ${variantId}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update grouped product variant`,
        isLoading: false
      });
      return null;
    }
  },

  deleteGroupedProduct: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      console.log(`Store - Deleting grouped product with ID ${id}...`);
      const deletedProduct = await groupedProductApi.deleteGroupedProduct(id);
      console.log(`Store - Deleted grouped product with ID ${id}:`, deletedProduct);

      // Remove the product from the products list
      set(state => ({
        products: state.products.filter(p => p.id !== id),
        isLoading: false
      }));

      // Remove the product from any category lists it belongs to
      set(state => ({
        productsByCategory: Object.fromEntries(
          Object.entries(state.productsByCategory).map(([categoryId, products]) => [
            categoryId,
            products.filter(p => p.id !== id)
          ])
        )
      }));

      return deletedProduct;
    } catch (error) {
      console.error(`Store error - deleteGroupedProduct (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete grouped product with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  }
}));
