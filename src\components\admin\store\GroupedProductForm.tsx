"use client";

import { useState, useEffect, useCallback } from "react";
import { useProductStore } from "@/hooks/useProductStore";
import { useCategoryStore } from "@/hooks/useCategoryStore";

// Helper function to format dates for the API
const formatDateForAPI = (dateString: string): string | null => {
  if (!dateString) return null;

  try {
    // If the date already has seconds and timezone, return it as is
    if (dateString.includes('Z') || dateString.includes('+')) {
      return dateString;
    }

    // Parse the date and format it as ISO string with timezone
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return null; // Invalid date
    }

    // Return full ISO string with timezone
    return date.toISOString();
  } catch (error) {
    console.error("Error formatting date:", error);
    return null;
  }
};
import {
  Product,
  ProductImage,
  StockStatus,
  TaxStatus,
  TaxClass,
  ProductType,
  AccessLevel,
  ProductAttribute,
  AttributeValue,
  ProductAttributeWithValues,
  ProductVariant,
  GroupedProductData,
  ProductListing
} from "@/services/api";
import { FiAlertTriangle, FiPlus, FiX, FiArrowRight, FiArrowLeft } from "react-icons/fi";
import AttributeStep from "./grouped-product/AttributeStep";
import AttributeValuesStep from "./grouped-product/AttributeValuesStep";
import VariantsStep from "./grouped-product/VariantsStep";
import RichTextEditor from "../common/RichTextEditor";
import SeoEvaluator from "../common/SeoEvaluator";
import ListingsManager from "./ListingsManager";
import ImageManager from "../common/ImageManager";
import VariantProductForm from "./VariantProductForm";

interface GroupedProductFormProps {
  mode: "add" | "edit";
  onClose: () => void;
  categoryId?: number | null;
}

const GroupedProductForm = ({ mode, onClose, categoryId }: GroupedProductFormProps) => {
  const { selectedProduct, createGroupedProduct, updateGroupedProduct, setSelectedProduct, clearError } = useProductStore();
  const { categories, fetchCategories } = useCategoryStore();

  // Form steps
  const [currentStep, setCurrentStep] = useState(1);
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Basic product data
  const [basicProductData, setBasicProductData] = useState({
    sku: "",
    name: "",
    description: "",
    shortDescription: "",
    price: "",
    salePrice: "",
    saleStart: "",
    saleEnd: "",
    stockQuantity: 1,
    stockStatus: StockStatus.IN_STOCK,
    taxStatus: TaxStatus.TAXABLE,
    taxClass: TaxClass.STANDARD,
    access: AccessLevel.PUBLIC,
    password: "",
    tags: [] as string[],
    images: [] as ProductImage[],
    categoryIds: categoryId ? [categoryId] : [] as number[],
    listings: [] as ProductListing[],
  });

  const [isUnlimitedQuantity, setIsUnlimitedQuantity] = useState(false);

  const [deleteListingIds, setDeleteListingIds] = useState<number[]>([]);

  // Attributes and variants data
  const [selectedAttributes, setSelectedAttributes] = useState<ProductAttribute[]>([]);
  const [attributeValues, setAttributeValues] = useState<Record<number, AttributeValue[]>>({});
  const [productVariants, setProductVariants] = useState<ProductVariant[]>([]);

  // Load categories if not already loaded
  useEffect(() => {
    if (categories.length === 0) {
      fetchCategories();
    }
  }, [categories.length, fetchCategories]);

  // Load product data if in edit mode
  useEffect(() => {
    if (mode === "edit" && selectedProduct) {
      console.log("Loading grouped product data for editing:", selectedProduct);

      // Cast selectedProduct to any to access the properties we need
      const groupedProduct = selectedProduct as any;

      // Extract categoryIds from categories array
      let categoryIds: number[] = [];

      if (groupedProduct.categories && groupedProduct.categories.length > 0) {
        // If we have categories array, extract the IDs
        categoryIds = groupedProduct.categories
          .filter((cat: any) => cat && cat.id !== undefined)
          .map((cat: any) => cat.id as number);

        console.log("Extracted categoryIds from categories:", categoryIds);
      } else if (groupedProduct.categoryIds && groupedProduct.categoryIds.length > 0) {
        // If we have categoryIds array, use it directly
        categoryIds = groupedProduct.categoryIds;
        console.log("Using existing categoryIds:", categoryIds);
      }

      // Extract tags
      let tags: string[] = [];
      if (groupedProduct.tags) {
        if (Array.isArray(groupedProduct.tags)) {
          tags = groupedProduct.tags.map((tag: any) =>
            typeof tag === 'string' ? tag : tag.name
          );
        }
      }

      // Check if stockQuantity is null (unlimited)
      const isUnlimited = groupedProduct.stockQuantity === null;
      setIsUnlimitedQuantity(isUnlimited);

      // Set basic product data
      setBasicProductData({
        sku: groupedProduct.sku || "",
        name: groupedProduct.name || "",
        description: groupedProduct.description || "",
        shortDescription: groupedProduct.shortDescription || "",
        price: groupedProduct.price || "",
        salePrice: groupedProduct.salePrice || "",
        saleStart: groupedProduct.saleStart || "",
        saleEnd: groupedProduct.saleEnd || "",
        stockQuantity: isUnlimited ? 1 : (groupedProduct.stockQuantity > 0 ? groupedProduct.stockQuantity : 1),
        stockStatus: groupedProduct.stockStatus || StockStatus.IN_STOCK,
        taxStatus: groupedProduct.taxStatus || TaxStatus.TAXABLE,
        taxClass: groupedProduct.taxClass || TaxClass.STANDARD,
        access: groupedProduct.access || AccessLevel.PUBLIC,
        password: groupedProduct.password || "",
        images: groupedProduct.images || [],
        categoryIds: categoryIds,
        tags: tags,
        listings: groupedProduct.listings || []
      });

      // Load product attributes and values
      if (groupedProduct.ProductAttribute && groupedProduct.ProductAttribute.length > 0) {
        // Extract attributes
        const attributes = groupedProduct.ProductAttribute.map((pa: any) => ({
          id: pa.attribute.id,
          name: pa.attribute.name
        }));

        setSelectedAttributes(attributes);

        // Extract attribute values
        const values: Record<number, AttributeValue[]> = {};
        groupedProduct.ProductAttribute.forEach((pa: any) => {
          if (pa.values && pa.values.length > 0) {
            values[pa.attribute.id] = pa.values.map((v: any) => ({
              id: v.id,
              value: v.value,
              productAttributeId: pa.id
            }));
          }
        });

        setAttributeValues(values);
      }

      // Load variants
      if (groupedProduct.variants && groupedProduct.variants.length > 0) {
        const variants: ProductVariant[] = groupedProduct.variants.map((v: any) => ({
          id: v.id,
          sku: v.sku,
          price: v.price,
          salePrice: v.salePrice || "",
          stockQuantity: v.stockQuantity !== null ? v.stockQuantity : null,
          stockStatus: v.stockStatus,
          attributeValueIds: v.attributes.map((a: any) => a.value.id),
          images: v.ProductImage?.map((img: any) => ({
            url: img.url,
            position: img.position || 0
          })) || []
        }));

        setProductVariants(variants);
      }

      console.log("Loaded grouped product data for editing successfully");
    }
  }, [mode, selectedProduct]);

  // Utility function to clean price values
  const cleanPriceValue = (value: string): string => {
    // Remove all non-numeric characters except decimal point
    return value.replace(/[^0-9.]/g, '');
  };

  // Handle basic product data changes
  const handleBasicDataChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    // Special handling for stockQuantity to ensure it's a number
    if (name === 'stockQuantity') {
      // Parse as integer and ensure it's at least 1
      const numValue = parseInt(value, 10);
      const validValue = !isNaN(numValue) && numValue > 0 ? numValue : 1;

      setBasicProductData({
        ...basicProductData,
        [name]: validValue,
      });
    } else if (name === "price" || name === "salePrice") {
      // Clean price values to remove commas, dollar signs, and other non-numeric characters
      const cleanedValue = cleanPriceValue(value);
      setBasicProductData({
        ...basicProductData,
        [name]: cleanedValue,
      });
    } else {
      // Normal handling for other fields
      setBasicProductData({
        ...basicProductData,
        [name]: value,
      });
    }
  };

  // Handle unlimited quantity toggle
  const handleUnlimitedQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isUnlimited = e.target.checked;
    setIsUnlimitedQuantity(isUnlimited);

    if (isUnlimited) {
      // When unlimited is checked, set stockQuantity to 1 in the form (will be null in API)
      setBasicProductData({
        ...basicProductData,
        stockQuantity: 1,
      });
    } else {
      // When unlimited is unchecked, keep the current value or set to 1
      setBasicProductData({
        ...basicProductData,
        stockQuantity: basicProductData.stockQuantity || 1,
      });
    }
  };

  // Handle rich text editor changes
  const handleRichTextChange = (name: string, value: string) => {
    setBasicProductData({
      ...basicProductData,
      [name]: value,
    });
  };

  // Handle listings changes
  const handleListingsChange = (listings: ProductListing[], deleteIds?: number[]) => {
    setBasicProductData({
      ...basicProductData,
      listings
    });

    if (deleteIds && deleteIds.length > 0) {
      setDeleteListingIds(deleteIds);
    }
  };

  // Handle image URL changes
  const [imageUrl, setImageUrl] = useState("");

  // Handle tag input
  const [tagInput, setTagInput] = useState("");

  // Handle attribute values - memoized with useCallback
  const handleAttributeValues = useCallback((attributeId: number, values: AttributeValue[]) => {
    setAttributeValues(prev => ({
      ...prev,
      [attributeId]: values,
    }));
  }, []);

  // Handle variants - memoized with useCallback
  const handleVariantsChange = useCallback((variants: ProductVariant[]) => {
    setProductVariants(variants);
  }, []);

  // Only allow creation mode for GroupedProductForm
  if (mode === "edit") {
    return (
      <div className="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">
          Edit Grouped Product
        </h2>
        <div className="text-center py-8">
          <p className="text-gray-600 mb-4">
            Grouped products should be edited using separate forms for base product and variants.
          </p>
          <p className="text-sm text-gray-500">
            Use the ProductForm to edit the base product information and VariantProductForm to edit individual variants.
          </p>
          <button
            onClick={onClose}
            className="mt-4 px-6 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  const handleAddImage = () => {
    if (!imageUrl.trim()) {
      setFormError("Please enter an image URL");
      return;
    }

    const newImage: ProductImage = {
      url: imageUrl,
      position: basicProductData.images.length,
    };

    setBasicProductData({
      ...basicProductData,
      images: [...basicProductData.images, newImage],
    });
    setImageUrl("");
    setFormError(null);
  };

  const handleRemoveImage = (index: number) => {
    const updatedImages = [...basicProductData.images];
    updatedImages.splice(index, 1);

    // Update positions
    const reorderedImages = updatedImages.map((img, idx) => ({
      ...img,
      position: idx,
    }));

    setBasicProductData({
      ...basicProductData,
      images: reorderedImages,
    });
  };

  // Handle adding tags
  const handleAddTag = () => {
    if (!tagInput.trim()) return;

    // Don't add duplicate tags
    if (basicProductData.tags?.includes(tagInput.trim())) {
      setTagInput("");
      return;
    }

    setBasicProductData({
      ...basicProductData,
      tags: [...(basicProductData.tags || []), tagInput.trim()]
    });

    setTagInput("");
  };

  // Handle removing tags
  const handleRemoveTag = (tagToRemove: string) => {
    setBasicProductData({
      ...basicProductData,
      tags: basicProductData.tags?.filter(tag => tag !== tagToRemove) || []
    });
  };

  // Handle tag input keydown (add tag on Enter)
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle category selection
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = e.target.options;
    const selectedCategories: number[] = [];

    for (let i = 0; i < options.length; i++) {
      if (options[i].selected) {
        selectedCategories.push(parseInt(options[i].value));
      }
    }

    setBasicProductData({
      ...basicProductData,
      categoryIds: selectedCategories,
    });
  };

  // Handle attribute selection
  const handleAttributeSelection = (attributes: ProductAttribute[]) => {
    // Find removed attributes
    const removedAttributes = selectedAttributes.filter(
      oldAttr => !attributes.some(newAttr => newAttr.id === oldAttr.id)
    );

    // Clean up attribute values for removed attributes
    if (removedAttributes.length > 0) {
      setAttributeValues(prev => {
        const newValues = { ...prev };
        removedAttributes.forEach(attr => {
          if (attr.id && newValues[attr.id]) {
            delete newValues[attr.id];
          }
        });
        return newValues;
      });

      // Reset variants to force regeneration
      setProductVariants([]);
    }

    setSelectedAttributes(attributes);
  };



  // Navigate between steps
  const goToNextStep = () => {
    if (currentStep === 1) {
      // Validate basic product data
      if (!basicProductData.sku.trim()) {
        setFormError("SKU is required");
        return;
      }
      if (!basicProductData.name.trim()) {
        setFormError("Product name is required");
        return;
      }
      if (!basicProductData.price.trim()) {
        setFormError("Price is required");
        return;
      }
      if (basicProductData.categoryIds.length === 0) {
        setFormError("Please select at least one category");
        return;
      }

      setCurrentStep(2);
      setFormError(null);
    } else if (currentStep === 2) {
      // Validate attributes
      if (selectedAttributes.length === 0) {
        setFormError("Please select at least one attribute");
        return;
      }

      setCurrentStep(3);
      setFormError(null);
    } else if (currentStep === 3) {
      // Validate attribute values
      const allAttributesHaveValues = selectedAttributes.every(
        attr => attributeValues[attr.id!]?.length > 0
      );

      if (!allAttributesHaveValues) {
        setFormError("Please add at least one value for each attribute");
        return;
      }

      setCurrentStep(4);
      setFormError(null);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setFormError(null);
    }
  };

  // Submit the form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (currentStep !== 4) {
      goToNextStep();
      return;
    }

    // Validate variants
    if (productVariants.length === 0) {
      setFormError("Please configure at least one product variant");
      return;
    }

    setIsSubmitting(true);
    setFormError(null);

    try {
      // Create a copy of basicProductData with proper data types
      const processedBasicData = {
        ...basicProductData,
        // Handle stockQuantity - null for unlimited, number for limited
        stockQuantity: isUnlimitedQuantity ? null : (
          typeof basicProductData.stockQuantity === 'string'
            ? parseInt(basicProductData.stockQuantity, 10) || 1
            : basicProductData.stockQuantity || 1
        ),
        // Ensure salePrice is a valid decimal or null
        salePrice: basicProductData.salePrice ? basicProductData.salePrice : null,
        // Ensure dates are properly formatted with timezone
        saleStart: basicProductData.saleStart ? formatDateForAPI(basicProductData.saleStart) : null,
        saleEnd: basicProductData.saleEnd ? formatDateForAPI(basicProductData.saleEnd) : null,
      };

      let result: Product | null = null;

      if (mode === "add") {
        // Prepare product attributes with values for new products
        const productAttributes: ProductAttributeWithValues[] = selectedAttributes.map(attr => ({
          attributeId: attr.id!,
          values: attributeValues[attr.id!] || [],
        }));

        // Process variants for new products
        const processedVariants = productVariants.map((variant, index) => {
          const processedVariant = {
            ...variant,
            stockQuantity: variant.stockQuantity === null ? null : (
              typeof variant.stockQuantity === 'string'
                ? parseInt(variant.stockQuantity, 10) || 1
                : variant.stockQuantity || 1
            )
          };
          console.log(`Variant ${index} processing:`, {
            original: variant.stockQuantity,
            processed: processedVariant.stockQuantity
          });
          return processedVariant;
        });

        // Create the grouped product data for new products
        const groupedProductData = {
          ...processedBasicData,
          type: ProductType.GROUPED,
          productAttributes,
          variants: processedVariants,
        } as GroupedProductData;

        // Only include password if access is PROTECTED
        if (groupedProductData.access !== AccessLevel.PROTECTED) {
          delete groupedProductData.password;
        }

        result = await createGroupedProduct(groupedProductData);
        console.log("Created grouped product:", result);
      } else if (mode === "edit" && selectedProduct?.id) {
        // For edit mode, only send basic product data
        const updateData = {
          name: processedBasicData.name,
          sku: processedBasicData.sku,
          description: processedBasicData.description,
          shortDescription: processedBasicData.shortDescription,
          price: processedBasicData.price,
          salePrice: processedBasicData.salePrice || undefined,
          saleStart: processedBasicData.saleStart || undefined,
          saleEnd: processedBasicData.saleEnd || undefined,
          stockQuantity: processedBasicData.stockQuantity,
          stockStatus: processedBasicData.stockStatus,
          taxStatus: processedBasicData.taxStatus,
          taxClass: processedBasicData.taxClass,
          access: processedBasicData.access,
          password: processedBasicData.access === AccessLevel.PROTECTED ? processedBasicData.password : undefined,
          categoryIds: processedBasicData.categoryIds,
          tags: processedBasicData.tags,
          images: processedBasicData.images
        };

        // Only proceed with update if there are actual changes
        if (Object.keys(updateData).length === 0) {
          console.log("No changes detected, skipping update");
          setFormError("No changes detected. Please modify some fields before updating.");
          return;
        }

        console.log("Updating grouped product with data:", JSON.stringify(updateData, null, 2));

        // Call the update function
        result = await updateGroupedProduct(selectedProduct.id, updateData);
        console.log("Updated grouped product:", result);
      }

      if (!result) {
        throw new Error("Failed to save grouped product. No response received from server.");
      }

      // Reset form and close
      setBasicProductData({
        sku: "",
        name: "",
        description: "",
        shortDescription: "",
        price: "",
        salePrice: "",
        saleStart: "",
        saleEnd: "",
        stockQuantity: 1,
        stockStatus: StockStatus.IN_STOCK,
        taxStatus: TaxStatus.TAXABLE,
        taxClass: TaxClass.STANDARD,
        access: AccessLevel.PUBLIC,
        password: "",
        tags: [],
        images: [],
        categoryIds: [],
        listings: [],
      });
      setDeleteListingIds([]);
      setIsUnlimitedQuantity(false);
      setSelectedAttributes([]);
      setAttributeValues({});
      setProductVariants([]);
      setSelectedProduct(null);
      onClose();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setFormError(errorMessage);
      console.error("GroupedProductForm - Form submission error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setBasicProductData({
      sku: "",
      name: "",
      description: "",
      shortDescription: "",
      price: "",
      salePrice: "",
      saleStart: "",
      saleEnd: "",
      stockQuantity: 1,
      stockStatus: StockStatus.IN_STOCK,
      taxStatus: TaxStatus.TAXABLE,
      taxClass: TaxClass.STANDARD,
      access: AccessLevel.PUBLIC,
      password: "",
      tags: [],
      images: [],
      categoryIds: [],
      listings: [],
    });
    setDeleteListingIds([]);
    setIsUnlimitedQuantity(false);
    setSelectedAttributes([]);
    setAttributeValues({});
    setProductVariants([]);
    setSelectedProduct(null);
    clearError();
    setFormError(null);
    onClose();
  };

  return (
    <div className="max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">
        {mode === "add" ? "Add New Grouped Product" : "Edit Grouped Product"}
      </h2>

      {formError && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center shadow-sm">
          <FiAlertTriangle className="mr-3 flex-shrink-0 text-red-500" size={20} />
          <span className="font-medium">{formError}</span>
        </div>
      )}

      <div className="mb-8">
        <div className="flex justify-between items-center">
          {[1, 2, 3, 4].map((step) => (
            <div
              key={step}
              className={`flex flex-col items-center ${
                currentStep === step ? "text-main-color font-medium" : "text-gray-500"
              }`}
            >
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center mb-2 shadow-sm transition-all duration-300 ${
                  currentStep === step
                    ? "bg-main-color text-white scale-110"
                    : currentStep > step
                    ? "bg-green-500 text-white"
                    : "bg-gray-100 text-gray-500"
                }`}
              >
                {currentStep > step ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  step
                )}
              </div>
              <span className="text-sm">
                {step === 1
                  ? "Basic Info"
                  : step === 2
                  ? "Attributes"
                  : step === 3
                  ? "Attribute Values"
                  : "Variants"}
              </span>
            </div>
          ))}
        </div>
        <div className="mt-4 h-1.5 bg-gray-200 rounded-full relative">
          <div
            className="absolute h-1.5 bg-main-color rounded-full transition-all duration-500 ease-in-out"
            style={{ width: `${((currentStep - 1) / 3) * 100}%` }}
          ></div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Step 1: Basic Product Information */}
        {currentStep === 1 && (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <h3 className="text-lg font-medium border-b pb-2">Basic Information</h3>

                <div className="mb-4">
                  <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-2">
                    SKU *
                  </label>
                  <input
                    type="text"
                    id="sku"
                    name="sku"
                    value={basicProductData.sku}
                    onChange={handleBasicDataChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                  />
                </div>

                <div className="mb-8">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Product Name *
                  </label>
                  <div className="flex items-center gap-2">
                    <div className="flex-grow">
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={basicProductData.name}
                        onChange={handleBasicDataChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                      />
                    </div>
                    {basicProductData.name && basicProductData.name.length > 3 && (
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 rounded-full bg-gray-100 border flex items-center justify-center relative">
                          <div className="text-sm font-bold" id="titleSeoScore">
                            {/* Score will be updated by JS */}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="mt-4">
                    <SeoEvaluator
                      title={basicProductData.name}
                      content={basicProductData.name}
                      type="title"
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                    Base Price *
                  </label>
                  <input
                    type="text"
                    id="price"
                    name="price"
                    value={basicProductData.price}
                    onChange={handleBasicDataChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    placeholder="0.00"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-2">
                    Sale Price
                  </label>
                  <input
                    type="text"
                    id="salePrice"
                    name="salePrice"
                    value={basicProductData.salePrice}
                    onChange={handleBasicDataChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    placeholder="Leave empty if no sale"
                  />
                </div>

                <div className="grid grid-cols-2 gap-6 mb-4">
                  <div>
                    <label htmlFor="saleStart" className="block text-sm font-medium text-gray-700 mb-2">
                      Sale Start Date
                    </label>
                    <input
                      type="datetime-local"
                      id="saleStart"
                      name="saleStart"
                      value={basicProductData.saleStart}
                      onChange={handleBasicDataChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    />
                  </div>

                  <div>
                    <label htmlFor="saleEnd" className="block text-sm font-medium text-gray-700 mb-2">
                      Sale End Date
                    </label>
                    <input
                      type="datetime-local"
                      id="saleEnd"
                      name="saleEnd"
                      value={basicProductData.saleEnd}
                      onChange={handleBasicDataChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6 mb-4">
                  <div>
                    <label htmlFor="stockQuantity" className="block text-sm font-medium text-gray-700 mb-2">
                      Stock Quantity
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="unlimitedQuantity"
                          checked={isUnlimitedQuantity}
                          onChange={handleUnlimitedQuantityChange}
                          className="h-4 w-4 text-main-color focus:ring-main-color border-gray-300 rounded"
                        />
                        <label htmlFor="unlimitedQuantity" className="ml-2 text-sm text-gray-700">
                          Unlimited quantity
                        </label>
                      </div>
                      <input
                        type="number"
                        id="stockQuantity"
                        name="stockQuantity"
                        value={basicProductData.stockQuantity}
                        onChange={handleBasicDataChange}
                        min="1"
                        disabled={isUnlimitedQuantity}
                        className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color ${
                          isUnlimitedQuantity ? 'bg-gray-100 text-gray-500' : ''
                        }`}
                        placeholder={isUnlimitedQuantity ? 'Unlimited' : '1'}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="stockStatus" className="block text-sm font-medium text-gray-700 mb-2">
                      Stock Status *
                    </label>
                    <select
                      id="stockStatus"
                      name="stockStatus"
                      value={basicProductData.stockStatus}
                      onChange={handleBasicDataChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      {Object.values(StockStatus).map((status) => (
                        <option key={status} value={status}>
                          {status.replace('_', ' ')}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6 mb-4">
                  <div>
                    <label htmlFor="taxStatus" className="block text-sm font-medium text-gray-700 mb-2">
                      Tax Status
                    </label>
                    <select
                      id="taxStatus"
                      name="taxStatus"
                      value={basicProductData.taxStatus}
                      onChange={handleBasicDataChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      {Object.values(TaxStatus).map((status) => (
                        <option key={status} value={status}>
                          {status.replace('_', ' ')}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label htmlFor="taxClass" className="block text-sm font-medium text-gray-700 mb-2">
                      Tax Class
                    </label>
                    <select
                      id="taxClass"
                      name="taxClass"
                      value={basicProductData.taxClass}
                      onChange={handleBasicDataChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      {Object.values(TaxClass).map((taxClass) => (
                        <option key={taxClass} value={taxClass}>
                          {taxClass.replace('_', ' ')}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-6 mb-4">
                  <div>
                    <label htmlFor="access" className="block text-sm font-medium text-gray-700 mb-2">
                      Access Level
                    </label>
                    <select
                      id="access"
                      name="access"
                      value={basicProductData.access}
                      onChange={handleBasicDataChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      {Object.values(AccessLevel).map((level) => (
                        <option key={level} value={level}>
                          {level}
                        </option>
                      ))}
                    </select>
                  </div>

                  {basicProductData.access === AccessLevel.PROTECTED && (
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                        Password
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={basicProductData.password}
                        onChange={handleBasicDataChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                        placeholder="Enter password for protected access"
                      />
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product Tags
                  </label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {basicProductData.tags && basicProductData.tags.length > 0 ? (
                      basicProductData.tags.map((tag, index) => (
                        <div key={index} className="bg-gray-100 px-3 py-1 rounded-full flex items-center">
                          <span className="text-sm">{tag}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tag)}
                            className="ml-2 text-gray-500 hover:text-red-500"
                          >
                            <FiX size={14} />
                          </button>
                        </div>
                      ))
                    ) : (
                      <p className="text-xs text-gray-500">No tags added yet</p>
                    )}
                  </div>
                  <div className="flex">
                    <input
                      type="text"
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      onKeyDown={handleTagInputKeyDown}
                      placeholder="Enter tag and press Enter"
                      className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    />
                    <button
                      type="button"
                      onClick={handleAddTag}
                      className="bg-main-color text-white px-4 py-2 rounded-r-md hover:bg-main-color/90 transition"
                    >
                      <FiPlus />
                    </button>
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    Add tags to help customers find your product
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <h3 className="text-lg font-medium border-b pb-2">Additional Information</h3>

                <div className="mb-6">
                  <label htmlFor="categories" className="block text-sm font-medium text-gray-700 mb-2">
                    Categories *
                  </label>
                  <select
                    id="categories"
                    name="categories"
                    multiple
                    value={basicProductData.categoryIds.map(String)}
                    onChange={handleCategoryChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    size={4}
                  >
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-500 mt-2">Hold Ctrl/Cmd to select multiple categories</p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <h3 className="text-lg font-medium border-b pb-2">Product Images</h3>

              <ImageManager
                images={basicProductData.images}
                onImagesChange={(images) => setBasicProductData({ ...basicProductData, images })}
                uploadType="product-image"
                maxImages={10}
                allowReorder={true}
              />
              <p className="text-xs text-gray-500 mt-2">
                Upload at least one image for your product. You can also add URLs manually below.
              </p>

              {/* Manual URL input as fallback */}
              <div className="flex items-end space-x-3">
                <div className="flex-1">
                  <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-2">
                    Or add image URL manually
                  </label>
                  <input
                    type="text"
                    id="imageUrl"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
                <button
                  type="button"
                  onClick={handleAddImage}
                  className="px-4 py-2 bg-main-color text-white rounded-md flex items-center"
                >
                  <FiPlus className="mr-1" /> Add
                </button>
              </div>
            </div>

            {/* Short Description - Full Width Row */}
            <div className="mb-14 mt-8 pt-8 border-t">
              <label htmlFor="shortDescription" className="block text-sm font-medium text-gray-700 mb-2">
                Short Description
              </label>
              <div className="flex items-center gap-2">
                <div className="flex-grow">
                  <RichTextEditor
                    value={basicProductData.shortDescription}
                    onChange={(value) => handleRichTextChange('shortDescription', value)}
                    placeholder="Enter short product description..."
                    height="150px"
                  />
                </div>
                {basicProductData.shortDescription && basicProductData.shortDescription.length > 10 && (
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 rounded-full bg-gray-100 border flex items-center justify-center relative">
                      <div className="text-sm font-bold" id="shortDescriptionSeoScore">
                        {/* Score will be updated by JS */}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-2 mb-4">A brief summary that appears in product listings</p>
              <div className="mt-4">
                <SeoEvaluator
                  title={basicProductData.name}
                  content={basicProductData.shortDescription}
                  type="shortDescription"
                  keyword={basicProductData.name.split(' ')[0]}
                />
              </div>
            </div>

            {/* Full Description - Full Width Row */}
            <div className="mb-10 mt-12 pt-12 border-t">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Full Description
              </label>
              <div className="flex items-center gap-2">
                <div className="flex-grow">
                  <RichTextEditor
                    value={basicProductData.description}
                    onChange={(value) => handleRichTextChange('description', value)}
                    placeholder="Enter product description..."
                    height="200px"
                  />
                </div>
                {basicProductData.description && basicProductData.description.length > 10 && (
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 rounded-full bg-gray-100 border flex items-center justify-center relative">
                      <div className="text-sm font-bold" id="descriptionSeoScore">
                        {/* Score will be updated by JS */}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <p className="text-xs text-gray-500 mt-2 mb-4">Use the toolbar to format text with bold, italic, lists, and more</p>
              <div className="mt-6">
                <SeoEvaluator
                  title={basicProductData.name}
                  content={basicProductData.description}
                  type="description"
                  keyword={basicProductData.name.split(' ')[0]}
                />
              </div>
            </div>

            {/* Additional Information (Listings) */}
            <div className="mb-10 mt-12 pt-12 border-t">
              <ListingsManager
                listings={basicProductData.listings || []}
                onChange={handleListingsChange}
              />
            </div>
          </div>
        )}

        {/* Step 2: Attributes */}
        {currentStep === 2 && (
          <AttributeStep
            selectedAttributes={selectedAttributes}
            onAttributesChange={handleAttributeSelection}
          />
        )}

        {/* Step 3: Attribute Values */}
        {currentStep === 3 && (
          <AttributeValuesStep
            attributes={selectedAttributes}
            attributeValues={attributeValues}
            onAttributeValuesChange={handleAttributeValues}
          />
        )}

        {/* Step 4: Variants */}
        {currentStep === 4 && (
          <VariantsStep
            attributes={selectedAttributes}
            attributeValues={attributeValues}
            variants={productVariants}
            onVariantsChange={handleVariantsChange}
            productSku={basicProductData.sku}
          />
        )}

        <div className="flex justify-between pt-8 mt-8 border-t">
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-3 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors flex items-center"
            >
              Cancel
            </button>
            {currentStep > 1 && (
              <button
                type="button"
                onClick={goToPreviousStep}
                className="px-6 py-3 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center"
              >
                <FiArrowLeft className="mr-2" /> Back
              </button>
            )}
          </div>
          <button
            type="button"
            onClick={currentStep < 4 ? goToNextStep : handleSubmit}
            disabled={isSubmitting}
            className={`px-6 py-3 bg-main-color text-white rounded-md flex items-center hover:bg-opacity-90 transition-colors ${
              isSubmitting ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            {isSubmitting ? (
              "Processing..."
            ) : currentStep < 4 ? (
              <>
                Next <FiArrowRight className="ml-2" />
              </>
            ) : (
              "Save Product"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default GroupedProductForm;
