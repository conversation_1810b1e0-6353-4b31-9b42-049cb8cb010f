import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_AUTH_PATH_PREFIX = '/api/auth';

// Log the API configuration to help with debugging
console.log('Auth API - Base URL:', API_BASE_URL);
console.log('Auth API - Path Prefix:', API_AUTH_PATH_PREFIX);

// GET handler for fetching user profile
export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];

    console.log(`API proxy - Fetching user profile`);

    const response = await fetch(`${API_BASE_URL}${API_AUTH_PATH_PREFIX}/profile`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch profile' },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    // Return the response with the same status code
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (GET /auth/profile):', error);
    return NextResponse.json(
      { error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}
