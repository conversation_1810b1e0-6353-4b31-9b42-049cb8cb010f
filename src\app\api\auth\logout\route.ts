import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_AUTH_PATH_PREFIX = '/api/auth';

// Log the API configuration to help with debugging
console.log('Auth API - Base URL:', API_BASE_URL);
console.log('Auth API - Path Prefix:', API_AUTH_PATH_PREFIX);

// POST handler for user logout
export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const token = authHeader.split(' ')[1];

    console.log(`API proxy - Logging out user`);

    const response = await fetch(`${API_BASE_URL}${API_AUTH_PATH_PREFIX}/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    // If the backend doesn't have a logout endpoint, we can just return a success message
    if (response.status === 404) {
      return NextResponse.json(
        { message: 'Logged out successfully' },
        { status: 200 }
      );
    }

    const data = await response.json();
    
    // Return the response with the same status code
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /auth/logout):', error);
    // Even if there's an error, we consider the user logged out on the frontend
    return NextResponse.json(
      { message: 'Logged out successfully' },
      { status: 200 }
    );
  }
}
