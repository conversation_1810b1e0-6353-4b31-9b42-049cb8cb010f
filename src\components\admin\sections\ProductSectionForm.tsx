"use client";

import { useState, useEffect } from "react";
import { useProductSectionStore } from "@/hooks/useSectionStore";
import { ProductSection } from "@/services/api";
import { FiAlertTriangle } from "react-icons/fi";
import LoadingSpinner from "../common/LoadingSpinner";

interface ProductSectionFormProps {
  mode: "add" | "edit";
  sectionId?: number | null;
  onClose: () => void;
}

const ProductSectionForm = ({ mode, sectionId, onClose }: ProductSectionFormProps) => {
  const { selectedSection, fetchSectionById, createSection, updateSection, clearError } = useProductSectionStore();
  const [formData, setFormData] = useState<{ name: string; position: number }>({
    name: "",
    position: 0
  });
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    clearError();
    setFormError(null);

    const loadSection = async () => {
      if (mode === "edit" && sectionId) {
        setIsLoading(true);
        try {
          const section = await fetchSectionById(sectionId);
          if (section) {
            setFormData({
              name: section.name,
              position: section.position
            });
          } else {
            setFormError("Failed to load section data");
          }
        } catch (error) {
          setFormError(error instanceof Error ? error.message : "Failed to load section data");
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadSection();
  }, [mode, sectionId, fetchSectionById, clearError]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'position' ? parseInt(value) || 0 : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setIsSubmitting(true);

    try {
      if (!formData.name.trim()) {
        throw new Error("Section name is required");
      }

      if (mode === "add") {
        await createSection(formData);
      } else if (mode === "edit" && sectionId) {
        await updateSection(sectionId, formData);
      }

      onClose();
    } catch (error) {
      setFormError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">
        {mode === "add" ? "Add New Product Section" : "Edit Product Section"}
      </h2>

      {formError && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
          <FiAlertTriangle className="text-red-500 mt-0.5 mr-2 flex-shrink-0" />
          <p className="text-sm text-red-600">{formError}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Section Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-main-color focus:border-main-color"
            required
          />
        </div>

        <div>
          <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
            Position *
          </label>
          <input
            type="number"
            id="position"
            name="position"
            value={formData.position}
            onChange={handleChange}
            min={0}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-main-color focus:border-main-color"
            required
          />
          <p className="mt-1 text-sm text-gray-500">
            Lower numbers appear first. Sections are ordered by position.
          </p>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                {mode === "add" ? "Creating..." : "Updating..."}
              </div>
            ) : (
              mode === "add" ? "Create Section" : "Update Section"
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductSectionForm;
