"use client";

import { useState, useEffect } from 'react';
import { FiX, FiDollarSign } from 'react-icons/fi';

interface PriceFilterProps {
  minPrice: number;
  maxPrice: number;
  onPriceChange: (min: number, max: number) => void;
  className?: string;
}

const PriceFilter = ({ minPrice, maxPrice, onPriceChange, className = '' }: PriceFilterProps) => {
  const [localMin, setLocalMin] = useState(minPrice);
  const [localMax, setLocalMax] = useState(maxPrice);
  const [isDragging, setIsDragging] = useState<'min' | 'max' | null>(null);

  useEffect(() => {
    setLocalMin(minPrice);
    setLocalMax(maxPrice);
  }, [minPrice, maxPrice]);

  const handleMouseDown = (type: 'min' | 'max') => {
    setIsDragging(type);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;

    const slider = document.getElementById('price-slider');
    if (!slider) return;

    const rect = slider.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const value = Math.round(minPrice + percentage * (maxPrice - minPrice));

    if (isDragging === 'min') {
      const newMin = Math.min(value, localMax - 1);
      setLocalMin(newMin);
    } else {
      const newMax = Math.max(value, localMin + 1);
      setLocalMax(newMax);
    }
  };

  const handleMouseUp = () => {
    if (isDragging) {
      onPriceChange(localMin, localMax);
      setIsDragging(null);
    }
  };

  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, localMin, localMax]);

  const minPercentage = ((localMin - minPrice) / (maxPrice - minPrice)) * 100;
  const maxPercentage = ((localMax - minPrice) / (maxPrice - minPrice)) * 100;

  return (
    <div className={`inline-block bg-white rounded-lg shadow-sm border p-3 ${className}`}>
      <div className="flex items-center gap-2 mb-3">
        <FiDollarSign className="text-main-color" size={16} />
        <h3 className="text-sm font-medium text-gray-800">Price Range</h3>
        {(localMin > minPrice || localMax < maxPrice) && (
          <button
            onClick={() => {
              setLocalMin(minPrice);
              setLocalMax(maxPrice);
              onPriceChange(minPrice, maxPrice);
            }}
            className="ml-auto flex items-center gap-1 text-gray-400 hover:text-red-500 transition-colors"
          >
            <FiX size={14} />
            <span className="text-xs">Reset</span>
          </button>
        )}
      </div>

      <div className="relative h-6 mb-2">
        <div
          id="price-slider"
          className="absolute top-1/2 -translate-y-1/2 w-full h-0.5 bg-gray-100 rounded-full"
        >
          <div
            className="absolute h-full bg-main-color rounded-full"
            style={{
              left: `${minPercentage}%`,
              right: `${100 - maxPercentage}%`,
            }}
          />
          <div
            className="absolute w-3 h-3 bg-white border-2 border-main-color rounded-full cursor-pointer -translate-x-1/2 -translate-y-1/2 hover:scale-110 transition-transform"
            style={{ left: `${minPercentage}%` }}
            onMouseDown={() => handleMouseDown('min')}
          />
          <div
            className="absolute w-3 h-3 bg-white border-2 border-main-color rounded-full cursor-pointer -translate-x-1/2 -translate-y-1/2 hover:scale-110 transition-transform"
            style={{ left: `${maxPercentage}%` }}
            onMouseDown={() => handleMouseDown('max')}
          />
        </div>
      </div>

      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-1">
          <span className="text-main-color font-medium">${localMin}</span>
          <span>-</span>
          <span className="text-main-color font-medium">${localMax}</span>
        </div>
      </div>
    </div>
  );
};

export default PriceFilter; 