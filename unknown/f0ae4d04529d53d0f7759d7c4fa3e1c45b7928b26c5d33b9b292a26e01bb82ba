import { FiBox, FiDollarSign, FiShoppingBag, FiUsers } from "react-icons/fi";

const DashboardPage = () => {
  const stats = [
    {
      title: "Total Sales",
      value: "$12,345",
      icon: FiDollarSign,
      change: "+12%",
    },
    {
      title: "Total Orders",
      value: "156",
      icon: FiShoppingBag,
      change: "+8%",
    },
    {
      title: "Total Products",
      value: "89",
      icon: FiBox,
      change: "+5%",
    },
    {
      title: "Total Customers",
      value: "2,345",
      icon: FiUsers,
      change: "+15%",
    },
  ];

  return (
    <div>
      <h1 className="text-2xl font-bold mb-8">Dashboard Overview</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div
              key={index}
              className="bg-white p-6 rounded-xl shadow-sm border border-gray-100"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-500 text-sm">{stat.title}</p>
                  <h2 className="text-2xl font-bold mt-2">{stat.value}</h2>
                </div>
                <div className="bg-main-color/10 p-3 rounded-full">
                  <Icon className="w-6 h-6 text-main-color" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-green-500 text-sm">{stat.change}</span>
                <span className="text-gray-500 text-sm"> vs last month</span>
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 bg-white p-6 rounded-xl shadow-sm border border-gray-100">
        <h2 className="text-xl font-semibold mb-4">Recent Orders</h2>
        <p className="text-gray-500">Coming soon...</p>
      </div>
    </div>
  );
};

export default DashboardPage;