"use client";

import { useEffect, useState } from "react";
import { useCategoryStore } from "@/hooks/useCategoryStore";
import { FiEdit2, FiTrash2, FiAlertCircle, FiRefreshCw, FiImage } from "react-icons/fi";
import DeleteCategoryModal from "./DeleteCategoryModal";
import { Category } from "@/services/api";
import Image from "next/image";

interface CategoryListProps {
  onEdit: () => void;
  onViewProducts: (categoryId: number) => void;
}

const CategoryList = ({ onEdit, onViewProducts }: CategoryListProps) => {
  const { categories: categoriesFromStore, isLoading, error, fetchCategories, setSelectedCategory, clearError } = useCategoryStore();
  // Ensure categories is always an array
  const categories = Array.isArray(categoriesFromStore) ? categoriesFromStore : [];
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);

  useEffect(() => {
    const loadCategories = async () => {
      try {
        await fetchCategories();
        setFetchError(null);
      } catch (err) {
        setFetchError(err instanceof Error ? err.message : 'Failed to load categories');
      }
    };

    loadCategories();
  }, [fetchCategories]);

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    onEdit();
  };

  const handleDelete = (category: Category) => {
    setCategoryToDelete(category);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setCategoryToDelete(null);
  };

  const handleRetry = async () => {
    clearError();
    setFetchError(null);
    try {
      await fetchCategories();
    } catch (err) {
      setFetchError(err instanceof Error ? err.message : 'Failed to load categories');
    }
  };

  // Display loading state
  if (isLoading && categories.length === 0) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
      </div>
    );
  }

  // Display error state
  if ((error || fetchError) && categories.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="flex justify-center mb-4">
          <FiAlertCircle className="text-red-500 w-12 h-12" />
        </div>
        <h3 className="text-lg font-medium text-red-800 mb-2">Error loading categories</h3>
        <p className="text-red-600 mb-4">{error || fetchError}</p>
        <button
          onClick={handleRetry}
          className="mt-2 bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition flex items-center justify-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Retry
        </button>
      </div>
    );
  }

  // Display empty state
  if (categories.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>No categories found. Add your first category to get started.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                ID
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Slug
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {categories.map((category) => (
              <tr key={category.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {category.id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {category.imageUrl ? (
                    <div className="relative h-10 w-10 rounded-md overflow-hidden">
                      <Image
                        src={category.imageUrl}
                        alt={category.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ) : (
                    <FiImage className="h-10 w-10 text-gray-300 p-2 border border-gray-200 rounded-md" />
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => onViewProducts(category.id!)}
                    className="text-gray-900 hover:text-blue-600 font-medium hover:underline text-left"
                    title="View Products in this Category"
                  >
                    {category.name}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {category.slug}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                  {category.description}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => onViewProducts(category.id!)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                    title="View Products"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => handleEdit(category)}
                    className="text-indigo-600 hover:text-indigo-900 mr-3"
                    title="Edit Category"
                  >
                    <FiEdit2 className="inline-block" />
                  </button>
                  <button
                    onClick={() => handleDelete(category)}
                    className="text-red-600 hover:text-red-900"
                    title="Delete Category"
                  >
                    <FiTrash2 className="inline-block" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <DeleteCategoryModal
        isOpen={isDeleteModalOpen}
        category={categoryToDelete}
        onClose={closeDeleteModal}
      />
    </div>
  );
};

export default CategoryList;
