"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/hooks/useAuthStore";

const ProfilePage = () => {
  const router = useRouter();
  const { user, token, isLoading, logout, getProfile, isAuthenticated } = useAuthStore();
  const [isLogoutLoading, setIsLogoutLoading] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      // If user is not authenticated, redirect to login page
      if (!isAuthenticated()) {
        router.push("/login");
        return;
      }

      // Fetch user profile if not already loaded
      if (!user) {
        try {
          await getProfile();
        } catch (error) {
          console.error("Error fetching user profile:", error);
        }
      }
    };

    checkAuth();
  }, [isAuthenticated, user, getProfile, router]);

  const handleLogout = async () => {
    setIsLogoutLoading(true);
    await logout();
    setIsLogoutLoading(false);
    router.push("/login");
  };

  if (!isAuthenticated()) {
    return null; // Don't render anything if not authenticated
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">My Profile</h1>

          {isLoading ? (
            <div className="text-center py-8">Loading profile...</div>
          ) : user ? (
            <div className="space-y-6">
              <div className="border-b border-gray-200 pb-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Account Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="text-base font-medium">{user.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p className="text-base font-medium">{user.name || 'Not set'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Account Type</p>
                    <p className="text-base font-medium">
                      {user.isSuperAdmin ? 'Super Admin' : user.isAdmin ? 'Admin' : 'Customer'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={handleLogout}
                  disabled={isLogoutLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-70"
                >
                  {isLogoutLoading ? "Logging out..." : "Logout"}
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-red-500">
              Failed to load profile. Please try again later.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
