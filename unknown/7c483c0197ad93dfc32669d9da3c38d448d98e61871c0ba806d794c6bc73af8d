"use client";

import { useState } from "react";
import { useAuthStore } from "@/hooks/useAuthStore";
import { Fi<PERSON>lertTriangle, FiUserPlus } from "react-icons/fi";
import { CreateAdminRequest } from "@/services/authApi";

const AdminManagement = () => {
  const { user, createAdmin, isLoading, error, clearError } = useAuthStore();
  
  const [formData, setFormData] = useState<CreateAdminRequest>({
    email: "",
    password: "",
    name: "",
    firstName: "",
    lastName: "",
    isSuperAdmin: false
  });
  
  const [formError, setFormError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setSuccessMessage(null);
    clearError();
    
    // Validate form
    if (!formData.email.trim()) {
      setFormError("Email is required");
      return;
    }
    
    if (!formData.password.trim() || formData.password.length < 6) {
      setFormError("Password must be at least 6 characters");
      return;
    }
    
    try {
      const result = await createAdmin(formData);
      if (result) {
        setSuccessMessage(`Admin user ${result.email} created successfully`);
        // Reset form
        setFormData({
          email: "",
          password: "",
          name: "",
          firstName: "",
          lastName: "",
          isSuperAdmin: false
        });
        setTimeout(() => setSuccessMessage(null), 5000);
      }
    } catch (err) {
      setFormError(err instanceof Error ? err.message : "Failed to create admin user");
    }
  };

  // Only super admins can create other admins
  if (!user?.isSuperAdmin) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">Admin Management</h2>
        <div className="p-4 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded-md">
          <p>You need super admin privileges to manage admin users.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-6 text-gray-800 border-b pb-3">Admin Management</h2>
      
      {(formError || error) && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-md flex items-center shadow-sm">
          <FiAlertTriangle className="mr-3 flex-shrink-0 text-red-500" size={20} />
          <span className="font-medium">{formError || error}</span>
        </div>
      )}
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-md flex items-center shadow-sm">
          <span className="font-medium">{successMessage}</span>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>
          
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
              Password *
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Display Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>
          
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
              First Name
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>
          
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-main-color"
            />
          </div>
        </div>
        
        <div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isSuperAdmin"
              name="isSuperAdmin"
              checked={formData.isSuperAdmin}
              onChange={handleChange}
              className="h-4 w-4 text-main-color focus:ring-main-color border-gray-300 rounded"
            />
            <label htmlFor="isSuperAdmin" className="ml-2 block text-sm text-gray-700">
              Super Admin (can create other admins)
            </label>
          </div>
        </div>
        
        <div className="pt-4">
          <button
            type="submit"
            disabled={isLoading}
            className={`px-6 py-3 bg-main-color text-white rounded-md flex items-center hover:bg-opacity-90 transition-colors ${
              isLoading ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            <FiUserPlus className="mr-2" />
            {isLoading ? "Creating..." : "Create Admin User"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AdminManagement;
