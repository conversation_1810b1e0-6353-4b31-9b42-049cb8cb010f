import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
// API path prefix
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// GET handler for fetching all categories
export async function GET(request: NextRequest) {
  try {
    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/categories`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    let data;
    try {
      data = await response.json();
      console.log('Categories API response:', data);
    } catch (e) {
      console.error('Error parsing JSON response for categories:', e);
      // Return empty array if we can't parse the response
      return NextResponse.json([]);
    }

    // If the response is not an array, convert it to an empty array
    if (!Array.isArray(data)) {
      console.warn(`Expected array but got ${typeof data} for categories`);
      return NextResponse.json([]);
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('API proxy error (GET /categories):', error);
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

// POST handler for creating a new category
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}${API_PATH_PREFIX}/categories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('API proxy error (POST /categories):', error);
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    );
  }
}
