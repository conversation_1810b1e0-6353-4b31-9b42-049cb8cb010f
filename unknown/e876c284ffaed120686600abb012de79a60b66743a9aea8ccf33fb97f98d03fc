"use client";

import { useEffect, useState } from "react";
import { useProductSectionStore } from "@/hooks/useSectionStore";
import { FiEdit2, FiTrash2, FiAlertCircle, FiRefreshCw, FiEye, FiArrowUp, FiArrowDown } from "react-icons/fi";
import { ProductSection } from "@/services/api";
import DeleteProductSectionModal from "./DeleteProductSectionModal";
import LoadingSpinner from "../common/LoadingSpinner";

interface ProductSectionListProps {
  onEdit: (section: ProductSection) => void;
  onViewItems: (sectionId: number) => void;
}

const ProductSectionList = ({ onEdit, onViewItems }: ProductSectionListProps) => {
  const { sections, isLoading, error, fetchSections, setSelectedSection, clearError } = useProductSectionStore();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<ProductSection | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);

  useEffect(() => {
    const loadSections = async () => {
      try {
        await fetchSections();
      } catch (error) {
        setFetchError(error instanceof Error ? error.message : 'Failed to fetch product sections');
      }
    };

    loadSections();
  }, [fetchSections]);

  const handleEdit = (section: ProductSection) => {
    setSelectedSection(section);
    onEdit(section);
  };

  const handleDelete = (section: ProductSection) => {
    setSectionToDelete(section);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setSectionToDelete(null);
  };

  const handleRefresh = async () => {
    clearError();
    setFetchError(null);
    try {
      await fetchSections();
    } catch (error) {
      setFetchError(error instanceof Error ? error.message : 'Failed to fetch product sections');
    }
  };

  if (isLoading && sections.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if ((error || fetchError) && sections.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <FiAlertCircle className="text-red-500 w-12 h-12 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load product sections</h3>
        <p className="text-sm text-gray-500 mb-4">{error || fetchError}</p>
        <button
          onClick={handleRefresh}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-main-color hover:bg-main-color/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-main-color"
        >
          <FiRefreshCw className="mr-2 -ml-1 h-4 w-4" />
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div>
      {isLoading && (
        <div className="flex justify-center mb-4">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-sm text-gray-500">Refreshing...</span>
        </div>
      )}

      {sections.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No product sections found</h3>
          <p className="text-sm text-gray-500">Create your first product section to get started.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Position
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Products
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sections.map((section) => (
                <tr key={section.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {section.position}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => onViewItems(section.id!)}
                      className="text-gray-900 hover:text-blue-600 font-medium hover:underline text-left"
                      title="View Products in this Section"
                    >
                      {section.name}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {section.items?.length || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleEdit(section)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="Edit Section"
                      >
                        <FiEdit2 size={18} />
                      </button>
                      <button
                        onClick={() => onViewItems(section.id!)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Products"
                      >
                        <FiEye size={18} />
                      </button>
                      <button
                        onClick={() => handleDelete(section)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Section"
                      >
                        <FiTrash2 size={18} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <DeleteProductSectionModal
        isOpen={isDeleteModalOpen}
        section={sectionToDelete}
        onClose={handleCloseDeleteModal}
      />
    </div>
  );
};

export default ProductSectionList;
