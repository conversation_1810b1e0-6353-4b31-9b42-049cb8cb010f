import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';

// GET handler for fetching a specific user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    console.log(`Users API - Fetching user with ID: ${id}`);

    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      console.error('Users API - No authorization header provided');
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const backendUrl = `${API_BASE_URL}/api/users/${id}`;

    console.log(`Users API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      cache: 'no-store' // Disable caching for dynamic data
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      
      // Handle specific error cases
      if (response.status === 401) {
        return NextResponse.json(
          { error: 'Unauthorized - Invalid or expired token' },
          { status: 401 }
        );
      }
      
      if (response.status === 403) {
        return NextResponse.json(
          { error: 'Forbidden - Insufficient permissions' },
          { status: 403 }
        );
      }

      if (response.status === 404) {
        return NextResponse.json(
          { error: `User with ID ${id} not found` },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch user' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Users API - Successfully fetched user ${id}`);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Users API - Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
