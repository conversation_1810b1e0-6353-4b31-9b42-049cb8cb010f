import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// GET handler for fetching wholesale filter metadata
export async function GET(request: NextRequest) {
  try {
    console.log('Wholesale Filters API - Fetching filter metadata');

    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/wholesale/filters`;

    console.log(`Wholesale Filters API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Disable caching for dynamic filtering
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch wholesale filter metadata' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Wholesale Filters API - Successfully fetched filter metadata');

    return NextResponse.json(data);
  } catch (error) {
    console.error('Wholesale Filters API - Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
