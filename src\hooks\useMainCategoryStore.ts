import { create } from "zustand";
import { MainCategory, mainCategoryApi } from "@/services/api";

interface MainCategoryState {
  mainCategories: MainCategory[];
  isLoading: boolean;
  error: string | null;
  selectedMainCategory: MainCategory | null;

  // Actions
  fetchMainCategories: () => Promise<MainCategory[]>;
  createMainCategory: (mainCategory: MainCategory) => Promise<MainCategory | null>;
  updateMainCategory: (id: number, mainCategory: Partial<MainCategory>) => Promise<MainCategory | null>;
  addCategoriesToMainCategory: (id: number, categoryIds: number[]) => Promise<MainCategory | null>;
  deleteMainCategory: (id: number) => Promise<MainCategory | null>;
  setSelectedMainCategory: (mainCategory: MainCategory | null) => void;
  clearError: () => void;
}

export const useMainCategoryStore = create<MainCategoryState>((set) => ({
  mainCategories: [],
  isLoading: false,
  error: null,
  selectedMainCategory: null,

  clearError: () => set({ error: null }),

  fetchMainCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      const mainCategories = await mainCategoryApi.getAll();
      // Ensure mainCategories is a valid array
      if (!Array.isArray(mainCategories)) {
        console.error('Invalid main categories format:', mainCategories);
        throw new Error('Invalid main categories format from API');
      }
      set({ mainCategories, isLoading: false });
      return mainCategories;
    } catch (error) {
      console.error('Store error - fetchMainCategories:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch main categories',
        isLoading: false,
        mainCategories: [] // Reset main categories on error
      });
      return [];
    }
  },

  createMainCategory: async (mainCategory: MainCategory) => {
    set({ isLoading: true, error: null });
    try {
      const newMainCategory = await mainCategoryApi.create(mainCategory);
      set(state => ({
        mainCategories: [...state.mainCategories, newMainCategory],
        isLoading: false
      }));
      return newMainCategory;
    } catch (error) {
      console.error('Store error - createMainCategory:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to create main category',
        isLoading: false
      });
      return null;
    }
  },

  updateMainCategory: async (id: number, mainCategory: Partial<MainCategory>) => {
    set({ isLoading: true, error: null });
    try {
      const updatedMainCategory = await mainCategoryApi.update(id, mainCategory);
      set(state => ({
        mainCategories: state.mainCategories.map(c => c.id === id ? updatedMainCategory : c),
        isLoading: false,
        selectedMainCategory: state.selectedMainCategory?.id === id ? updatedMainCategory : state.selectedMainCategory
      }));
      return updatedMainCategory;
    } catch (error) {
      console.error(`Store error - updateMainCategory (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to update main category with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  addCategoriesToMainCategory: async (id: number, categoryIds: number[]) => {
    set({ isLoading: true, error: null });
    try {
      const updatedMainCategory = await mainCategoryApi.addCategories(id, categoryIds);
      set(state => ({
        mainCategories: state.mainCategories.map(c => c.id === id ? updatedMainCategory : c),
        isLoading: false,
        selectedMainCategory: state.selectedMainCategory?.id === id ? updatedMainCategory : state.selectedMainCategory
      }));
      return updatedMainCategory;
    } catch (error) {
      console.error(`Store error - addCategoriesToMainCategory (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to add categories to main category with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  deleteMainCategory: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const deletedMainCategory = await mainCategoryApi.delete(id);
      set(state => ({
        mainCategories: state.mainCategories.filter(c => c.id !== id),
        isLoading: false,
        selectedMainCategory: state.selectedMainCategory?.id === id ? null : state.selectedMainCategory
      }));
      return deletedMainCategory;
    } catch (error) {
      console.error(`Store error - deleteMainCategory (ID: ${id}):`, error);
      set({
        error: error instanceof Error ? error.message : `Failed to delete main category with ID ${id}`,
        isLoading: false
      });
      return null;
    }
  },

  setSelectedMainCategory: (mainCategory: MainCategory | null) => {
    set({ selectedMainCategory: mainCategory });
  }
}));
