"use client";

import { useState } from 'react';
import { FiX, FiFilter } from 'react-icons/fi';
import PriceFilter from './PriceFilter';

interface FilterBarProps {
  onFilterChange: (filters: {
    priceRange: { min: number; max: number };
    onSale: boolean;
    isNew: boolean;
  }) => void;
  className?: string;
}

const FilterBar = ({ onFilterChange, className = '' }: FilterBarProps) => {
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: { min: 0, max: 1000 },
    onSale: false,
    isNew: false,
  });

  const handlePriceChange = (min: number, max: number) => {
    const newFilters = { ...filters, priceRange: { min, max } };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleFilterToggle = (key: 'onSale' | 'isNew') => {
    const newFilters = { ...filters, [key]: !filters[key] };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const resetFilters = () => {
    const newFilters = {
      priceRange: { min: 0, max: 1000 },
      onSale: false,
      isNew: false,
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const hasActiveFilters = filters.onSale || filters.isNew || 
    filters.priceRange.min > 0 || filters.priceRange.max < 1000;

  return (
    <div className={`bg-white rounded-lg shadow-sm border ${className}`}>
      <div className="p-3 border-b">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 text-gray-700 hover:text-main-color transition-colors"
          >
            <FiFilter size={18} />
            <span className="text-sm font-medium">Filters</span>
            {hasActiveFilters && (
              <span className="ml-1 px-2 py-0.5 bg-main-color text-white text-xs rounded-full">
                {[
                  filters.onSale && 'Sale',
                  filters.isNew && 'New',
                  (filters.priceRange.min > 0 || filters.priceRange.max < 1000) && 'Price'
                ].filter(Boolean).length}
              </span>
            )}
          </button>
          {hasActiveFilters && (
            <button
              onClick={resetFilters}
              className="flex items-center gap-1 text-gray-400 hover:text-red-500 transition-colors"
            >
              <FiX size={14} />
              <span className="text-xs">Reset All</span>
            </button>
          )}
        </div>
      </div>

      {showFilters && (
        <div className="p-3 space-y-4">
          {/* Price Filter */}
          <div className="flex justify-end">
            <PriceFilter
              minPrice={filters.priceRange.min}
              maxPrice={filters.priceRange.max}
              onPriceChange={handlePriceChange}
              className="w-64"
            />
          </div>

          {/* Additional Filters */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleFilterToggle('onSale')}
              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                filters.onSale
                  ? 'bg-main-color text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              On Sale
            </button>
            <button
              onClick={() => handleFilterToggle('isNew')}
              className={`px-3 py-1.5 rounded-full text-sm font-medium transition-colors ${
                filters.isNew
                  ? 'bg-main-color text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              New Arrivals
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FilterBar; 