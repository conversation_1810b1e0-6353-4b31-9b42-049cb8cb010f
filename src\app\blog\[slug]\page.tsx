import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getPostBySlug, allBlogPosts } from '@/data/blogData';
import BlogPostDetail from '@/components/blog/BlogPostDetail';

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateStaticParams() {
  return allBlogPosts.map((post) => ({
    slug: post.slug,
  }));
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = getPostBySlug(params.slug);

  if (!post) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  const { seo, title, excerpt, blogImage, author, createdDate } = post;

  return {
    title: seo.metaTitle || title,
    description: seo.metaDescription || excerpt,
    keywords: seo.keywords?.join(', ') || post.tags.join(', '),
    authors: [{ name: author.name }],
    openGraph: {
      title: seo.metaTitle || title,
      description: seo.metaDescription || excerpt,
      type: 'article',
      url: `/blog/${params.slug}`,
      images: [
        {
          url: blogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      publishedTime: createdDate,
      authors: [author.name],
      tags: post.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: seo.metaTitle || title,
      description: seo.metaDescription || excerpt,
      images: [blogImage],
    },
  };
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = getPostBySlug(params.slug);

  if (!post) {
    notFound();
  }

  // Generate JSON-LD structured data for SEO
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: post.blogImage,
    author: {
      '@type': 'Person',
      name: post.author.name,
      ...(post.author.avatar && { image: post.author.avatar }),
    },
    publisher: {
      '@type': 'Organization',
      name: 'CocoJojo',
      logo: {
        '@type': 'ImageObject',
        url: '/images/cocojojo.png',
      },
    },
    datePublished: post.createdDate,
    dateModified: post.updatedDate || post.createdDate,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `/blog/${params.slug}`,
    },
    keywords: post.tags.join(', '),
    wordCount: post.content.reduce((count, block) => {
      if (block.type === 'text') {
        return count + block.content.split(' ').length;
      }
      return count;
    }, 0),
    timeRequired: `PT${post.readTime}M`,
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <BlogPostDetail post={post} />
    </>
  );
}
