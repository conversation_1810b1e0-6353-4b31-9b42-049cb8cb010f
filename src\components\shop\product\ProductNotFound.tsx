"use client";

import { useRouter } from 'next/navigation';
import { <PERSON>ShoppingBag, FiArrowLeft, FiSearch } from 'react-icons/fi';
import Image from 'next/image';

interface ProductNotFoundProps {
  error?: string;
  slug?: string;
}

const ProductNotFound = ({ error, slug }: ProductNotFoundProps) => {
  const router = useRouter();

  return (
    <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16 flex flex-col items-center justify-center min-h-[60vh]">
      <div className="text-center max-w-2xl mx-auto">
        <div className="mb-8 relative w-40 h-40 mx-auto">
          <div className="absolute inset-0 flex items-center justify-center">
            <FiSearch size={80} className="text-gray-300" />
          </div>
        </div>
        
        <h1 className="text-3xl font-medium text-gray-800 mb-4">Product Not Found</h1>
        
        <p className="text-gray-600 mb-2">
          We couldn't find the product you're looking for.
        </p>
        
        {slug && (
          <p className="text-gray-500 mb-8">
            <span className="font-medium">"{slug}"</span> is not available or may have been removed.
          </p>
        )}
        
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <button 
            onClick={() => router.push('/shop')}
            className="w-full sm:w-auto bg-main-color text-white px-6 py-3 rounded-lg hover:bg-main-color/90 transition-colors flex items-center justify-center gap-2"
          >
            <FiShoppingBag size={18} />
            <span>Browse All Products</span>
          </button>
          
          <button 
            onClick={() => router.back()}
            className="w-full sm:w-auto bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors mt-4 sm:mt-0 flex items-center justify-center gap-2"
          >
            <FiArrowLeft size={18} />
            <span>Go Back</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductNotFound;
