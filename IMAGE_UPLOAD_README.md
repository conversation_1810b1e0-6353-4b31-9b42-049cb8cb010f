# Image Upload System Documentation

## Overview
This document describes the image upload functionality implemented for the CocoJojo e-commerce admin dashboard. The system allows uploading images to your VPS server and returns URLs that can be stored in the database.

## Architecture

### Backend (NestJS)
Your NestJS backend already provides the following upload endpoints:
- `POST /api/uploads/product-image` - Upload product images
- `POST /api/uploads/variant-image` - Upload variant images  
- `POST /api/uploads/category-image` - Upload category images
- `POST /api/uploads/blog-image` - Upload blog images
- `POST /api/uploads/multiple-images` - Upload multiple images

### Frontend (Next.js)
The frontend implementation includes:

#### API Routes (Proxy to NestJS)
- `/api/uploads/product-image` - Proxies to NestJS backend
- `/api/uploads/variant-image` - Proxies to NestJS backend
- `/api/uploads/multiple-images` - Proxies to NestJS backend

#### Components
1. **ImageUpload** - Basic upload component with drag & drop
2. **ImageManager** - Advanced image management with reordering
3. **Image Management Page** - Dedicated admin page for bulk uploads

#### Hooks
- **useImageUpload** - Custom hook for programmatic uploads

## Usage Examples

### 1. Basic Image Upload Component
```tsx
import ImageUpload from '@/components/admin/common/ImageUpload';

function MyComponent() {
  const handleUpload = (images) => {
    console.log('Uploaded images:', images);
    // Save URLs to your state/database
  };

  return (
    <ImageUpload
      onUpload={handleUpload}
      uploadType="product-image"
      multiple={true}
      maxFiles={5}
    />
  );
}
```

### 2. Advanced Image Manager
```tsx
import ImageManager from '@/components/admin/common/ImageManager';

function ProductForm() {
  const [images, setImages] = useState([]);

  return (
    <ImageManager
      images={images}
      onImagesChange={setImages}
      uploadType="product-image"
      maxImages={10}
      allowReorder={true}
    />
  );
}
```

### 3. Using the Upload Hook
```tsx
import { useImageUpload } from '@/hooks/useImageUpload';

function MyComponent() {
  const { uploadSingle, uploading, error } = useImageUpload({
    uploadType: 'product-image',
    subfolder: 'featured'
  });

  const handleFileSelect = async (file) => {
    const result = await uploadSingle(file);
    if (result) {
      console.log('Upload successful:', result.url);
    }
  };

  return (
    <div>
      <input type="file" onChange={(e) => handleFileSelect(e.target.files[0])} />
      {uploading && <p>Uploading...</p>}
      {error && <p>Error: {error}</p>}
    </div>
  );
}
```

## File Structure
```
src/
├── app/
│   ├── admin/dashboard/images/          # Image management page
│   └── api/uploads/                     # API proxy routes
│       ├── product-image/
│       ├── variant-image/
│       └── multiple-images/
├── components/admin/common/
│   ├── ImageUpload.tsx                  # Basic upload component
│   └── ImageManager.tsx                 # Advanced image manager
└── hooks/
    └── useImageUpload.ts                # Upload hook
```

## Integration with Existing Forms

### Product Form
The ProductForm has been updated to use ImageManager:
```tsx
<ImageManager
  images={formData.images}
  onImagesChange={(images) => setFormData({ ...formData, images })}
  uploadType="product-image"
  maxImages={10}
  allowReorder={true}
/>
```

### Variant Form
The VariantProductForm has been updated similarly:
```tsx
<ImageManager
  images={formData.images}
  onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
  uploadType="variant-image"
  maxImages={5}
  allowReorder={true}
/>
```

## Features

### Upload Methods
1. **Drag & Drop** - Drag files directly onto upload area
2. **Click to Select** - Click to open file browser
3. **Manual URL** - Enter image URLs manually as fallback

### File Validation
- **File Types**: JPEG, PNG, GIF, WebP
- **File Size**: Maximum 5MB per file
- **File Count**: Configurable maximum files per upload

### Image Management
- **Preview** - Thumbnail previews of uploaded images
- **Reordering** - Drag to reorder images (if enabled)
- **URL Copying** - Click to copy image URLs to clipboard
- **Deletion** - Remove unwanted images

### Error Handling
- File type validation
- File size validation
- Network error handling
- User-friendly error messages

## Environment Variables
Make sure these are set in your `.env.local`:
```env
NEXT_PUBLIC_API_BASE_URL=https://your-domain.com
# or for development:
# NEXT_PUBLIC_API_BASE_URL=http://localhost:3300
```

## Navigation
A new "Images" link has been added to the admin sidebar for easy access to the image management page.

## Response Format
All upload endpoints return the same format:
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "data": {
    "filename": "file-1703123456789-123456789.jpg",
    "originalName": "product-image.jpg",
    "size": 245760,
    "mimetype": "image/jpeg",
    "url": "https://your-domain.com/api/uploads/products/file-1703123456789-123456789.jpg",
    "path": "./uploads/products/file-1703123456789-123456789.jpg"
  }
}
```

## Security Considerations
- File type validation on both frontend and backend
- File size limits to prevent abuse
- Unique filename generation to prevent conflicts
- Proper error handling to avoid information leakage

## Future Enhancements
- Image optimization/resizing
- CDN integration
- Bulk delete functionality
- Image search and filtering
- Image metadata editing
