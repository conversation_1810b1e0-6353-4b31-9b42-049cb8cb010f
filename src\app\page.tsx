import Slider from "@/components/Slider"
import FeaturedCategories from "@/components/FeaturedCategories"
import ProductSectionDisplay from "@/components/shop/ProductSectionDisplay"
import HeroBanner from "@/components/home/<USER>"
import TrustBadges from "@/components/home/<USER>"
import WhyChooseUs from "@/components/home/<USER>"
import Newsletter from "@/components/home/<USER>"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Premium Organic Cosmetics & Beauty Products | CocoJojo",
  description: "Discover CocoJojo's premium organic cosmetics, beauty products, and wholesale solutions. From packaging to private labeling, we offer comprehensive beauty manufacturing services.",
  keywords: "organic cosmetics, beauty products, wholesale cosmetics, private labeling, contract manufacturing, cosmetic packaging, natural beauty, bulk cosmetics",
  openGraph: {
    title: "Premium Organic Cosmetics & Beauty Products | CocoJojo",
    description: "Discover CocoJojo's premium organic cosmetics, beauty products, and wholesale solutions.",
    type: "website",
    url: "/",
    images: [
      {
        url: "/images/cocojojo-og.jpg",
        width: 1200,
        height: 630,
        alt: "CocoJojo - Premium Organic Cosmetics",
      },
    ],
  },
}

const HomePage = () => {
  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <Slider />

      {/* Trust Badges */}
      <section className="py-12 bg-gray-50">
        <TrustBadges />
      </section>

      {/* Featured Products Section 1 */}
      <section className="py-16 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 bg-white">
        <ProductSectionDisplay position={1} />
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <WhyChooseUs />
      </section>

      {/* Featured Categories Section */}
      <section className="py-20 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 bg-white">
        <FeaturedCategories />
      </section>

      {/* Featured Products Section 2 */}
      <section className="py-16 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 bg-gray-50">
        <ProductSectionDisplay position={2} />
      </section>

      {/* Hero Banner - Call to Action */}
      <section className="py-20">
        <HeroBanner />
      </section>

      {/* Featured Products Section 3 */}
      <section className="py-16 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 bg-white">
        <ProductSectionDisplay position={3} />
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-gradient-to-r from-main-color to-main-color/90">
        <Newsletter />
      </section>
    </div>
  )
}

export default HomePage