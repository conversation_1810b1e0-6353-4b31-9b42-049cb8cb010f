import { NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Main Categories API - Base URL:', API_BASE_URL);
console.log('Main Categories API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching all main categories
export async function GET() {
  try {
    // Construct the backend URL
    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/main-categories`;

    console.log(`Main Categories API proxy - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // Add cache control for ISR
      next: { revalidate: 7200 } // 24 hours cache
    });

    // Check if the response is JSON
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      console.error('Invalid response type:', contentType);
      const responseText = await response.text();
      console.error('Response preview:', responseText.substring(0, 200));

      return NextResponse.json(
        {
          error: 'Backend server returned invalid response format',
          details: 'Expected JSON but received ' + (contentType || 'unknown content type')
        },
        { status: 502 }
      );
    }

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        {
          error: 'Failed to fetch main categories',
          details: errorData
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Main Categories API response received:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Main Categories API proxy error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch main categories' },
      { status: 500 }
    );
  }
}
