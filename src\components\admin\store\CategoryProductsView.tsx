"use client";

import { useEffect, useState } from "react";
import { useProductStore } from "@/hooks/useProductStore";
import { useCategoryStore } from "@/hooks/useCategoryStore";
import { FiArrowLeft, FiEdit2, FiTrash2, FiAlertCircle, FiRefreshCw, FiEye, FiSearch, FiFilter, FiX } from "react-icons/fi";
import { Product, StockStatus, Category, ProductType } from "@/services/api";
import DeleteProductModal from "./DeleteProductModal";
import ProductDetailModal from "./ProductDetailModal";

interface CategoryProductsViewProps {
  categoryId: number;
  onBack: () => void;
  onEdit: (isEdit?: boolean, product?: Product) => void;
  onAddGroupedProduct?: () => void;
}

const CategoryProductsView = ({ categoryId, onBack, onEdit, onAddGroupedProduct }: CategoryProductsViewProps) => {
  const { productsByCategory, categoryProductsPagination, isLoading, error, fetchProductsByCategory, setSelectedProduct, clearError } = useProductStore();
  const { categories, fetchCategories } = useCategoryStore();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<Product | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [category, setCategory] = useState<Category | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Debounce search term to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Get products for this category
  const products = productsByCategory[categoryId] || [];
  const pagination = categoryProductsPagination[categoryId];

  // Since search is now server-side, we don't need client-side filtering
  // All products from the API are already filtered by search term
  const filteredProducts = products;

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log(`Loading data for category ID ${categoryId}...`);

        // Load category if not already loaded
        if (categories.length === 0) {
          console.log('Categories not loaded, fetching categories...');
          await fetchCategories();
        }

        // Find the current category
        const currentCategory = categories.find(c => c.id === categoryId);
        if (currentCategory) {
          console.log(`Found category: ${currentCategory.name}`);
          setCategory(currentCategory);
        } else {
          console.warn(`Category with ID ${categoryId} not found in categories list`);
        }

        // Load products for this category
        console.log(`Fetching products for category ID ${categoryId} - page: ${currentPage}, limit: ${itemsPerPage}, search: ${debouncedSearchTerm || 'none'}`);
        const response = await fetchProductsByCategory(categoryId, currentPage, itemsPerPage, debouncedSearchTerm || undefined);
        if (response) {
          console.log(`Fetched ${response.data.length} products for category ID ${categoryId} (page ${currentPage})`);
        }

        setFetchError(null);
      } catch (err) {
        console.error(`Error loading data for category ID ${categoryId}:`, err);
        setFetchError(err instanceof Error ? err.message : 'Failed to load category products');
      }
    };

    loadData();
  }, [categoryId, categories, fetchCategories, fetchProductsByCategory, currentPage, itemsPerPage, debouncedSearchTerm]);

  console.log(`CategoryProductsView - Products for category ${categoryId}:`, products);
  console.log(`CategoryProductsView - All products by category:`, productsByCategory);

  const handleEdit = async (product: Product) => {
    try {
      // First set the selected product with the basic data we have
      setSelectedProduct(product);

      let completeProduct = null;

      // Then fetch the complete product data by ID
      if (product.id) {
        console.log(`Fetching complete product data for ID ${product.id}...`);
        completeProduct = await useProductStore.getState().fetchProductById(product.id);
        console.log(`Fetched complete product data:`, completeProduct);

        // Update the selected product with the complete data
        if (completeProduct) {
          setSelectedProduct(completeProduct);
        }
      }

      // Then call the onEdit function from props to switch to edit mode
      // Pass true to indicate this is an edit operation, and pass the product
      const productToEdit = completeProduct || product;
      onEdit(true, productToEdit);
    } catch (error) {
      console.error(`Error fetching complete product data:`, error);
      // Continue with the edit using the basic product data
      onEdit(true, product);
    }
  };

  const handleDelete = (product: Product) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setProductToDelete(null);
  };

  const handleViewDetails = (product: Product) => {
    if (product.id) {
      setSelectedProductId(product.id);
      setIsDetailModalOpen(true);
    }
  };

  const closeDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedProductId(null);
  };

  const handleRetry = async () => {
    clearError();
    setFetchError(null);
    try {
      await fetchProductsByCategory(categoryId, currentPage, itemsPerPage, debouncedSearchTerm || undefined);
    } catch (err) {
      setFetchError(err instanceof Error ? err.message : 'Failed to load category products');
    }
  };

  // Pagination handlers
  const handleNextPage = () => {
    if (pagination?.hasNext) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevPage = () => {
    if (pagination?.hasPrev) {
      setCurrentPage(prev => prev - 1);
    }
  };

  // Display loading state
  if (isLoading && products.length === 0) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-main-color"></div>
      </div>
    );
  }

  // Display error state
  if ((error || fetchError) && products.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="flex justify-center mb-4">
          <FiAlertCircle className="text-red-500 w-12 h-12" />
        </div>
        <h3 className="text-lg font-medium text-red-800 mb-2">Error loading products</h3>
        <p className="text-red-600 mb-4">{error || fetchError}</p>
        <button
          onClick={handleRetry}
          className="mt-2 bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition flex items-center justify-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Retry
        </button>
      </div>
    );
  }

  const getStockStatusClass = (status?: StockStatus) => {
    if (!status) {
      return "bg-gray-100 text-gray-800";
    }

    switch (status) {
      case StockStatus.IN_STOCK:
        return "bg-green-100 text-green-800";
      case StockStatus.OUT_OF_STOCK:
        return "bg-red-100 text-red-800";
      case StockStatus.ON_BACKORDER:
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Function to get row class based on product type
  const getProductRowClass = (product: Product) => {
    const baseClass = "hover:bg-gray-50 cursor-pointer";

    if (product.type === ProductType.GROUPED) {
      return `${baseClass} bg-indigo-50 border-l-4 border-indigo-500`;
    }

    return baseClass;
  };

  // Clear search
  const clearSearch = () => {
    setSearchTerm("");
  };

  return (
    <div>
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={onBack}
            className="mr-4 text-gray-600 hover:text-gray-900"
            title="Back to Categories"
          >
            <FiArrowLeft className="w-5 h-5" />
          </button>
          <h2 className="text-xl font-semibold">
            Products in {category?.name || `Category #${categoryId}`}
          </h2>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => {
              setSelectedProduct(null);
              onEdit(false); // Pass false to indicate this is an add operation
            }}
            className="bg-main-color text-white px-4 py-2 rounded-md hover:bg-main-color/90 transition flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Add Simple Product
          </button>

          {onAddGroupedProduct && (
            <button
              onClick={() => {
                setSelectedProduct(null);
                onAddGroupedProduct();
              }}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Grouped Product
            </button>
          )}
        </div>
      </div>

      {/* Search and Filter UI */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search input */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name or SKU..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-main-color focus:border-main-color sm:text-sm"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <FiX className="text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>
          </div>


        </div>


      </div>

      {/* Results count and pagination info */}
      <div className="mb-4 flex justify-between items-center">
        <div className="text-sm text-gray-600">
          {pagination ? (
            <>
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} products in {category?.name || `Category #${categoryId}`}
              {filteredProducts.length !== products.length && (
                <span className="text-orange-600"> (filtered to {filteredProducts.length} on this page)</span>
              )}
            </>
          ) : (
            `Showing ${filteredProducts.length} of ${products.length} products in ${category?.name || `Category #${categoryId}`}`
          )}
        </div>
        {pagination && (
          <div className="text-sm text-gray-500">
            Page {pagination.page} of {pagination.totalPages}
          </div>
        )}
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Image
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                SKU
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredProducts.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center text-gray-500">
                  {debouncedSearchTerm ? (
                    <div>
                      <p className="text-lg font-medium">No products found for "{debouncedSearchTerm}"</p>
                      <p className="mt-2">Try adjusting your search terms or browse all products in this category.</p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-lg font-medium">No products found in this category</p>
                      <p className="mt-2">Add products to this category to get started.</p>
                    </div>
                  )}
                </td>
              </tr>
            ) : (
              filteredProducts.map((product) => (
                <tr
                  key={product.id}
                  className={getProductRowClass(product)}
                  onClick={() => handleViewDetails(product)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="relative h-12 w-12 rounded overflow-hidden">
                      {product.images && product.images.length > 0 ? (
                        <img
                          src={product.images[0].url}
                          alt={product.name}
                          className="h-12 w-12 object-cover"
                        />
                      ) : (
                        <div className="h-12 w-12 bg-gray-200 flex items-center justify-center text-gray-500 text-xs">
                          No image
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {product.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {product.sku}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      product.type === ProductType.GROUPED
                        ? 'bg-indigo-100 text-indigo-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {product.type || ProductType.SIMPLE}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    ${product.price}
                    {product.salePrice && (
                      <span className="ml-2 line-through text-gray-400">${product.salePrice}</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStockStatusClass(product.stockStatus || StockStatus.OUT_OF_STOCK)}`}>
                      {product.stockStatus ? product.stockStatus.replace('_', ' ') : 'Unknown'} ({product.stockQuantity || 0})
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" onClick={(e) => e.stopPropagation()}>
                    <button
                      onClick={() => handleViewDetails(product)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                      title="View Product Details"
                    >
                      <FiEye className="inline-block" />
                    </button>
                    <button
                      onClick={() => handleEdit(product)}
                      className="text-indigo-600 hover:text-indigo-900 mr-3"
                      title="Edit Product"
                    >
                      <FiEdit2 className="inline-block" />
                    </button>
                    <button
                      onClick={() => handleDelete(product)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete Product"
                    >
                      <FiTrash2 className="inline-block" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-6 flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Page {pagination.page} of {pagination.totalPages}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handlePrevPage}
              disabled={!pagination.hasPrev}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
            >
              Previous
            </button>
            <button
              onClick={handleNextPage}
              disabled={!pagination.hasNext}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
            >
              Next
            </button>
          </div>
        </div>
      )}

      <DeleteProductModal
        isOpen={isDeleteModalOpen}
        product={productToDelete}
        onClose={closeDeleteModal}
      />

      <ProductDetailModal
        isOpen={isDetailModalOpen}
        productId={selectedProductId}
        onClose={closeDetailModal}
        onEdit={(product) => {
          setSelectedProduct(product);
          closeDetailModal();
          onEdit(true, product);
        }}
      />
    </div>
  );
};

export default CategoryProductsView;
