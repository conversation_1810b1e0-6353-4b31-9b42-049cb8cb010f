"use client";

import { useState } from 'react';
import { FiPlus, FiTrash2, FiCopy, FiCheck, FiImage, FiAlertCircle } from 'react-icons/fi';
import ImageUpload, { UploadedImage } from './ImageUpload';

interface ImageItem {
  id?: number;
  url: string;
  position: number;
  originalName?: string;
}

interface ImageManagerProps {
  images: ImageItem[];
  onImagesChange: (images: ImageItem[]) => void;
  uploadType?: 'product-image' | 'variant-image' | 'category-image' | 'blog-image';
  subfolder?: string;
  maxImages?: number;
  allowReorder?: boolean;
  className?: string;
}

const ImageManager = ({
  images,
  onImagesChange,
  uploadType = 'product-image',
  subfolder,
  maxImages = 10,
  allowReorder = true,
  className = ''
}: ImageManagerProps) => {
  const [showUpload, setShowUpload] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
  const [loadingImages, setLoadingImages] = useState<Set<number>>(new Set());

  const handleUpload = (uploadedImages: UploadedImage[]) => {
    console.log('ImageManager - Received uploaded images:', uploadedImages);

    const newImages: ImageItem[] = uploadedImages.map((img, index) => ({
      url: img.url,
      position: images.length + index,
      originalName: img.originalName
    }));

    console.log('ImageManager - Created new images:', newImages);
    onImagesChange([...images, ...newImages]);
    setShowUpload(false);
    setError(null);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const removeImage = (index: number) => {
    const updatedImages = images.filter((_, i) => i !== index);
    // Reorder positions
    const reorderedImages = updatedImages.map((img, i) => ({
      ...img,
      position: i
    }));
    onImagesChange(reorderedImages);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    if (!allowReorder) return;
    
    const updatedImages = [...images];
    const [movedImage] = updatedImages.splice(fromIndex, 1);
    updatedImages.splice(toIndex, 0, movedImage);
    
    // Update positions
    const reorderedImages = updatedImages.map((img, i) => ({
      ...img,
      position: i
    }));
    
    onImagesChange(reorderedImages);
  };

  const copyUrl = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(url);
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  const handleImageError = (index: number) => {
    setImageErrors(prev => new Set(prev).add(index));
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });
  };

  const handleImageLoad = (index: number) => {
    setImageErrors(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });
    setLoadingImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(index);
      return newSet;
    });
  };

  const handleImageLoadStart = (index: number) => {
    setLoadingImages(prev => new Set(prev).add(index));
  };

  const canAddMore = images.length < maxImages;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">
          Images ({images.length}/{maxImages})
        </h3>
        {canAddMore && (
          <button
            type="button"
            onClick={() => setShowUpload(!showUpload)}
            className="flex items-center gap-2 px-3 py-2 bg-main-color text-white rounded-md hover:bg-main-color/90 transition-colors"
          >
            <FiPlus size={16} />
            Add Images
          </button>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
          {error}
        </div>
      )}

      {/* Upload Component */}
      {showUpload && canAddMore && (
        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
          <ImageUpload
            onUpload={handleUpload}
            onError={handleError}
            multiple={true}
            maxFiles={maxImages - images.length}
            uploadType={uploadType}
            subfolder={subfolder}
          />
        </div>
      )}

      {/* Images Grid */}
      {images.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative group bg-white border border-gray-200 rounded-lg overflow-hidden"
            >
              {/* Position Badge */}
              <div className="absolute top-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded z-10">
                #{index + 1}
              </div>

              {/* Image */}
              <div className="aspect-square relative bg-gray-100">
                {imageErrors.has(index) ? (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <div className="text-center">
                      <FiAlertCircle className="mx-auto mb-2 text-gray-400" size={24} />
                      <p className="text-xs text-gray-500">Failed to load</p>
                      <p className="text-xs text-gray-400 mt-1 px-2 truncate" title={image.url}>
                        {image.url}
                      </p>
                    </div>
                  </div>
                ) : loadingImages.has(index) ? (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100">
                    <div className="text-center">
                      <FiImage className="mx-auto mb-2 text-gray-400 animate-pulse" size={24} />
                      <p className="text-xs text-gray-500">Loading...</p>
                    </div>
                  </div>
                ) : (
                  <img
                    src={image.url}
                    alt={image.originalName || `Image ${index + 1}`}
                    className="w-full h-full object-cover"
                    onLoadStart={() => {
                      console.log('Image load started:', image.url);
                      handleImageLoadStart(index);
                    }}
                    onError={(e) => {
                      console.error('Image failed to load:', image.url, e);
                      handleImageError(index);
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully:', image.url);
                      handleImageLoad(index);
                    }}
                    loading="lazy"
                  />
                )}
              </div>

              {/* Actions Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex gap-2">
                  {/* Copy URL */}
                  <button
                    type="button"
                    onClick={() => copyUrl(image.url)}
                    className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100 transition-colors"
                    title="Copy URL"
                  >
                    {copiedUrl === image.url ? (
                      <FiCheck size={16} className="text-green-600" />
                    ) : (
                      <FiCopy size={16} />
                    )}
                  </button>

                  {/* Remove */}
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                    title="Remove"
                  >
                    <FiTrash2 size={16} />
                  </button>
                </div>
              </div>

              {/* Reorder Controls */}
              {allowReorder && images.length > 1 && (
                <div className="absolute bottom-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index - 1)}
                      className="p-1 bg-white text-gray-700 rounded text-xs hover:bg-gray-100"
                      title="Move left"
                    >
                      ←
                    </button>
                  )}
                  {index < images.length - 1 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index + 1)}
                      className="p-1 bg-white text-gray-700 rounded text-xs hover:bg-gray-100"
                      title="Move right"
                    >
                      →
                    </button>
                  )}
                </div>
              )}

              {/* Image Info */}
              <div className="p-2 bg-white">
                <p className="text-xs text-gray-600 truncate" title={image.url}>
                  {image.originalName || 'Uploaded image'}
                </p>
                <p className="text-xs text-gray-400 truncate mt-1" title={image.url}>
                  {image.url}
                </p>
                {/* Test link to verify URL works */}
                <div className="flex gap-2 mt-1">
                  <a
                    href={image.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-500 hover:text-blue-700"
                  >
                    Test URL →
                  </a>
                  <button
                    type="button"
                    onClick={() => {
                      console.log('Testing image URL:', image.url);
                      // Create a test image to check if it loads
                      const testImg = new Image();
                      testImg.onload = () => console.log('✅ Image loads successfully:', image.url);
                      testImg.onerror = (e) => console.error('❌ Image failed to load:', image.url, e);
                      testImg.src = image.url;
                    }}
                    className="text-xs text-green-500 hover:text-green-700"
                  >
                    Test Load
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <FiImage size={48} className="mx-auto mb-2 text-gray-300" />
          <p>No images uploaded yet</p>
          <p className="text-sm">Click "Add Images" to get started</p>
        </div>
      )}
    </div>
  );
};

export default ImageManager;
