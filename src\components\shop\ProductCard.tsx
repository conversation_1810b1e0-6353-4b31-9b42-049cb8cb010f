"use client";

import { useState } from "react";
import Image from "next/image";
import { FiShoppingBag } from "react-icons/fi";
import { ensureValidSlug } from "@/utils/slugUtils";
import ProductAccessIndicator from "@/components/shop/ProductAccessIndicator";
import PasswordPromptModal from "@/components/shop/PasswordPromptModal";
import { useProtectedProduct } from "@/hooks/useProtectedProduct";

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice?: number;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  access: 'PUBLIC' | 'PROTECTED' | 'PRIVATE';
}

interface ProductCardProps {
  product: Product;
  viewMode: 'grid' | 'list';
  onAddToCart: (product: Product) => void;
  addingToCartId: number | null;
}

const ProductCard = ({ product, viewMode, onAddToCart, addingToCartId }: ProductCardProps) => {
  const {
    isPasswordModalOpen,
    isLoading,
    error,
    closePasswordModal,
    handlePasswordSubmit,
    handleProductClick
  } = useProtectedProduct({
    productSlug: product.slug,
    access: product.access,
    productName: product.name
  });

  const handleImageClick = (e: React.MouseEvent) => {
    e.preventDefault();
    handleProductClick();
  };

  const handleTitleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    handleProductClick();
  };

  if (viewMode === 'grid') {
    return (
      <>
        <div className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300">
          <div className="relative h-64 overflow-hidden rounded-t-lg">
            <div 
              onClick={handleImageClick}
              className="cursor-pointer w-full h-full"
            >
              <Image
                src={product.imageUrl}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            
            {/* Access indicator */}
            {product.access !== 'PUBLIC' && (
              <div className="absolute top-2 left-2">
                <ProductAccessIndicator access={product.access} size="sm" />
              </div>
            )}
            
            {product.salePrice && (
              <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-medium">
                Sale
              </div>
            )}
            {!product.inStock && (
              <div className="absolute bottom-2 right-2 bg-gray-500 text-white px-2 py-1 rounded text-sm font-medium">
                Out of Stock
              </div>
            )}
          </div>

          <div className="p-4">
            <div 
              onClick={handleTitleClick}
              className="cursor-pointer"
            >
              <h3 className="font-medium text-gray-800 mb-2 line-clamp-2 hover:text-main-color transition-colors">
                {product.name}
              </h3>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {product.salePrice ? (
                  <>
                    <span className="text-lg font-bold text-main-color">
                      ${product.salePrice.toFixed(2)}
                    </span>
                    <span className="text-sm text-gray-500 line-through">
                      ${product.price.toFixed(2)}
                    </span>
                  </>
                ) : (
                  <span className="text-lg font-bold text-gray-800">
                    ${product.price.toFixed(2)}
                  </span>
                )}
              </div>

              <button
                onClick={() => onAddToCart(product)}
                disabled={!product.inStock || addingToCartId === product.id || product.access === 'PRIVATE'}
                className={`p-2 rounded-full transition-colors ${
                  product.inStock && product.access !== 'PRIVATE'
                    ? "bg-main-color text-white hover:bg-main-color/90"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              >
                {addingToCartId === product.id ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <FiShoppingBag size={16} />
                )}
              </button>
            </div>
          </div>
        </div>

        <PasswordPromptModal
          isOpen={isPasswordModalOpen}
          onClose={closePasswordModal}
          onSubmit={handlePasswordSubmit}
          productName={product.name}
          isLoading={isLoading}
          error={error}
        />
      </>
    );
  }

  // List view
  return (
    <>
      <div className="group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 p-4">
        <div className="flex gap-4">
          <div className="relative w-24 h-24 flex-shrink-0 overflow-hidden rounded-lg">
            <div 
              onClick={handleImageClick}
              className="cursor-pointer w-full h-full"
            >
              <Image
                src={product.imageUrl}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0 mr-4">
                <div className="flex items-center gap-2 mb-1">
                  <div 
                    onClick={handleTitleClick}
                    className="cursor-pointer flex-1"
                  >
                    <h3 className="font-medium text-gray-800 hover:text-main-color transition-colors">
                      {product.name}
                    </h3>
                  </div>
                  {product.access !== 'PUBLIC' && (
                    <ProductAccessIndicator access={product.access} size="sm" />
                  )}
                </div>

                {product.shortDescription && (
                  <div
                    className="text-sm text-gray-600 mb-2 max-h-12 overflow-hidden leading-tight"
                    dangerouslySetInnerHTML={{ __html: product.shortDescription }}
                  />
                )}

                <div className="flex items-center space-x-2">
                  {product.salePrice ? (
                    <>
                      <span className="text-lg font-bold text-main-color">
                        ${product.salePrice.toFixed(2)}
                      </span>
                      <span className="text-sm text-gray-500 line-through">
                        ${product.price.toFixed(2)}
                      </span>
                    </>
                  ) : (
                    <span className="text-lg font-bold text-gray-800">
                      ${product.price.toFixed(2)}
                    </span>
                  )}
                </div>
              </div>

              <div className="flex flex-col items-end gap-2">
                {!product.inStock && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    Out of Stock
                  </span>
                )}
                
                <button
                  onClick={() => onAddToCart(product)}
                  disabled={!product.inStock || addingToCartId === product.id || product.access === 'PRIVATE'}
                  className={`p-2 rounded-full transition-colors ${
                    product.inStock && product.access !== 'PRIVATE'
                      ? "bg-main-color text-white hover:bg-main-color/90"
                      : "bg-gray-300 text-gray-500 cursor-not-allowed"
                  }`}
                >
                  {addingToCartId === product.id ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <FiShoppingBag size={16} />
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <PasswordPromptModal
        isOpen={isPasswordModalOpen}
        onClose={closePasswordModal}
        onSubmit={handlePasswordSubmit}
        productName={product.name}
        isLoading={isLoading}
        error={error}
      />
    </>
  );
};

export default ProductCard;
