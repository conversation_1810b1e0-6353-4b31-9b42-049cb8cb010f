"use client";

import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler,
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

// Common chart options
const commonOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
};

// Color palette for charts
export const chartColors = {
  primary: '#8B5CF6',
  secondary: '#06B6D4',
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  info: '#3B82F6',
  light: '#F3F4F6',
  dark: '#374151',
};

// Line Chart Component
interface LineChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor?: string;
      backgroundColor?: string;
      fill?: boolean;
    }[];
  };
  title?: string;
  height?: number;
  showGrid?: boolean;
}

export const LineChart: React.FC<LineChartProps> = ({ 
  data, 
  title, 
  height = 300,
  showGrid = true 
}) => {
  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: showGrid,
        },
      },
      y: {
        grid: {
          display: showGrid,
        },
        beginAtZero: true,
      },
    },
  };

  return (
    <div style={{ height: `${height}px` }}>
      <Line data={data} options={options} />
    </div>
  );
};

// Bar Chart Component
interface BarChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }[];
  };
  title?: string;
  height?: number;
  horizontal?: boolean;
}

export const BarChart: React.FC<BarChartProps> = ({ 
  data, 
  title, 
  height = 300,
  horizontal = false 
}) => {
  const options = {
    ...commonOptions,
    indexAxis: horizontal ? 'y' as const : 'x' as const,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
    },
    scales: {
      x: {
        beginAtZero: true,
      },
      y: {
        beginAtZero: true,
      },
    },
  };

  return (
    <div style={{ height: `${height}px` }}>
      <Bar data={data} options={options} />
    </div>
  );
};

// Pie Chart Component
interface PieChartProps {
  data: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor?: string[];
      borderColor?: string[];
      borderWidth?: number;
    }[];
  };
  title?: string;
  height?: number;
}

export const PieChart: React.FC<PieChartProps> = ({ 
  data, 
  title, 
  height = 300 
}) => {
  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
    },
  };

  return (
    <div style={{ height: `${height}px` }}>
      <Pie data={data} options={options} />
    </div>
  );
};

// Doughnut Chart Component
interface DoughnutChartProps {
  data: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor?: string[];
      borderColor?: string[];
      borderWidth?: number;
    }[];
  };
  title?: string;
  height?: number;
  centerText?: string;
}

export const DoughnutChart: React.FC<DoughnutChartProps> = ({ 
  data, 
  title, 
  height = 300,
  centerText 
}) => {
  const options = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: !!title,
        text: title,
        font: {
          size: 16,
          weight: 'bold' as const,
        },
      },
    },
    cutout: '60%',
  };

  return (
    <div style={{ height: `${height}px`, position: 'relative' }}>
      <Doughnut data={data} options={options} />
      {centerText && (
        <div 
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            pointerEvents: 'none',
          }}
          className="text-lg font-semibold text-gray-700"
        >
          {centerText}
        </div>
      )}
    </div>
  );
};

// Chart data helper functions
export const createLineChartData = (
  labels: string[],
  datasets: Array<{
    label: string;
    data: number[];
    color?: string;
    fill?: boolean;
  }>
) => ({
  labels,
  datasets: datasets.map((dataset, index) => ({
    label: dataset.label,
    data: dataset.data,
    borderColor: dataset.color || Object.values(chartColors)[index % Object.values(chartColors).length],
    backgroundColor: dataset.fill 
      ? `${dataset.color || Object.values(chartColors)[index % Object.values(chartColors).length]}20`
      : 'transparent',
    fill: dataset.fill || false,
    tension: 0.4,
  })),
});

export const createBarChartData = (
  labels: string[],
  datasets: Array<{
    label: string;
    data: number[];
    colors?: string[];
  }>
) => ({
  labels,
  datasets: datasets.map((dataset, index) => ({
    label: dataset.label,
    data: dataset.data,
    backgroundColor: dataset.colors || Object.values(chartColors),
    borderColor: dataset.colors || Object.values(chartColors),
    borderWidth: 1,
  })),
});

export const createPieChartData = (
  labels: string[],
  data: number[],
  colors?: string[]
) => ({
  labels,
  datasets: [{
    data,
    backgroundColor: colors || Object.values(chartColors),
    borderColor: colors || Object.values(chartColors),
    borderWidth: 2,
  }],
});
