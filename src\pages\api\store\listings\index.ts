import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { method } = req;
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3300';

  try {
    switch (method) {
      case 'POST': {
        // Create a new listing
        const response = await fetch(`${baseUrl}/store/listings`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(req.body),
        });

        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.message || 'Failed to create listing');
        }
        
        return res.status(response.status).json(data);
      }

      default:
        res.setHeader('Allow', ['POST']);
        return res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('API error in /api/store/listings:', error);
    return res.status(500).json({ message: error instanceof Error ? error.message : 'An error occurred' });
  }
}
