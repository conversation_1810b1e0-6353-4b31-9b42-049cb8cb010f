"use client";

import { FiTag } from 'react-icons/fi';

interface ProductTag {
  id: number;
  name: string;
  slug: string;
}

interface ProductTagsProps {
  tags: ProductTag[];
}

const ProductTags = ({ tags }: ProductTagsProps) => {
  return (
    <div>
      <h3 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
        <FiTag size={16} /> Tags
      </h3>
      <div className="flex flex-wrap gap-2">
        {tags.map(tag => (
          <span 
            key={tag.id} 
            className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors cursor-pointer"
          >
            {tag.name}
          </span>
        ))}
      </div>
    </div>
  );
};

export default ProductTags;
