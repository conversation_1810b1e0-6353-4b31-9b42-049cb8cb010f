"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FiList,
} from "react-icons/fi";
import OrdersList from "./OrdersList";
import OrderAnalytics from "./OrderAnalytics";
import OrderReports from "./OrderReports";
import OrderDetailModal from "./OrderDetailModal";
import { Order } from "@/services/api";

type TabType = "orders" | "analytics" | "reports";

const OrdersPageClient = () => {
  const [activeTab, setActiveTab] = useState<TabType>("orders");
  const [selectedOrderId, setSelectedOrderId] = useState<number | null>(null);
  const [isOrderModalOpen, setIsOrderModalOpen] = useState(false);

  // Handle order selection
  const handleOrderSelect = (order: Order) => {
    setSelectedOrderId(order.id);
    setIsOrderModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsOrderModalOpen(false);
    setSelectedOrderId(null);
  };

  // Tab configuration
  const tabs = [
    {
      id: "orders" as TabType,
      label: "Orders",
      icon: FiList,
      description: "View and manage all orders with advanced filtering and search",
    },
    {
      id: "analytics" as TabType,
      label: "Analytics",
      icon: FiBarChart,
      description: "Comprehensive analytics with charts and metrics for different time periods",
    },
    {
      id: "reports" as TabType,
      label: "Reports & Insights",
      icon: FiPieChart,
      description: "Detailed reports with top products, customers, and revenue insights",
    },
  ];

  // Render tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case "orders":
        return <OrdersList onOrderSelect={handleOrderSelect} />;
      case "analytics":
        return <OrderAnalytics />;
      case "reports":
        return <OrderReports />;
      default:
        return <OrdersList onOrderSelect={handleOrderSelect} />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FiShoppingBag className="text-main-color" size={32} />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Orders Management</h1>
            <p className="text-gray-600 mt-1">
              Comprehensive order management, analytics, and insights dashboard
            </p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="border-b border-gray-200">
          <nav className="flex">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group relative flex-1 px-6 py-4 text-left transition-all duration-200 ${
                    activeTab === tab.id
                      ? "bg-main-color text-white"
                      : "bg-white text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Icon 
                      size={20} 
                      className={`transition-transform duration-200 ${
                        activeTab === tab.id ? "scale-110" : "group-hover:scale-105"
                      }`} 
                    />
                    <div>
                      <div className="font-medium">{tab.label}</div>
                      <div 
                        className={`text-sm mt-1 ${
                          activeTab === tab.id 
                            ? "text-white/80" 
                            : "text-gray-500 group-hover:text-gray-700"
                        }`}
                      >
                        {tab.description}
                      </div>
                    </div>
                  </div>
                  
                  {/* Active tab indicator */}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/30"></div>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>

      {/* Order Detail Modal */}
      <OrderDetailModal
        isOpen={isOrderModalOpen}
        onClose={handleModalClose}
        orderId={selectedOrderId}
      />
    </div>
  );
};

export default OrdersPageClient;
