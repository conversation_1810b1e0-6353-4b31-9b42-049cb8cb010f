import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_AUTH_PATH_PREFIX = process.env.NEXT_PUBLIC_API_AUTH_PATH_PREFIX || '/api/auth';

// Log the API configuration to help with debugging
console.log('Users API - Base URL:', API_BASE_URL);
console.log('Users API - Auth Path Prefix:', API_AUTH_PATH_PREFIX);

// GET handler for fetching users with pagination and search
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    console.log('Users API - Fetching users');

    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      console.error('Users API - No authorization header provided');
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    // Forward all query parameters to the backend
    const queryParams = new URLSearchParams();

    // Copy all search parameters from the request
    searchParams.forEach((value, key) => {
      queryParams.append(key, value);
    });

    // Set default values if not provided
    if (!searchParams.has('page')) {
      queryParams.set('page', '1');
    }
    if (!searchParams.has('limit')) {
      queryParams.set('limit', '30');
    }

    const backendUrl = `${API_BASE_URL}/api/users?${queryParams.toString()}`;

    console.log(`Users API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      cache: 'no-store' // Disable caching for dynamic data
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      
      // Handle specific error cases
      if (response.status === 401) {
        return NextResponse.json(
          { error: 'Unauthorized - Invalid or expired token' },
          { status: 401 }
        );
      }
      
      if (response.status === 403) {
        return NextResponse.json(
          { error: 'Forbidden - Insufficient permissions' },
          { status: 403 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Users API - Successfully fetched users data');

    return NextResponse.json(data);
  } catch (error) {
    console.error('Users API - Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
