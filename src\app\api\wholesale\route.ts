import { NextRequest, NextResponse } from 'next/server';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Wholesale API - Base URL:', API_BASE_URL);
console.log('Wholesale API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching wholesale products
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    console.log('Wholesale API - Fetching wholesale products');

    // Forward all query parameters to the backend
    const queryParams = new URLSearchParams();

    // Copy all search parameters from the request
    searchParams.forEach((value, key) => {
      queryParams.append(key, value);
    });

    // Set default values if not provided
    if (!searchParams.has('page')) {
      queryParams.set('page', '1');
    }
    if (!searchParams.has('limit')) {
      queryParams.set('limit', '20');
    }

    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/wholesale?${queryParams.toString()}`;

    console.log(`Wholesale API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store' // Disable caching for dynamic filtering
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: 'Failed to fetch wholesale products' },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('Wholesale API - Successfully fetched data');

    return NextResponse.json(data);
  } catch (error) {
    console.error('Wholesale API - Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
